# Configuración de Producción para CCJAP Sistema de Gestión Docente
# Copia este archivo a .env.prod y configura los valores según tu entorno

# =============================================================================
# CONFIGURACIÓN DE DOMINIOS
# =============================================================================
FRONTEND_DOMAIN=ccjap.echolab.xyz
BACKEND_DOMAIN=api.ccjap.echolab.xyz
N8N_DOMAIN=n8n.echolab.xyz

# =============================================================================
# CONFIGURACIÓN DE BASE DE DATOS
# =============================================================================
DB_USER=ccjap_admin
DB_PASSWORD=K@rur0su24
DB_NAME=ccjap_db

# =============================================================================
# CONFIGURACIÓN DE AUTENTICACIÓN JWT
# =============================================================================
JWT_SECRET=K@rur0su24_JWT_SECRET_2025_PRODUCTION
JWT_EXPIRES_IN=24h

# =============================================================================
# CONFIGURACIÓN DE N8N
# =============================================================================
N8N_ENCRYPTION_KEY=K@rur0su24_N8N_ENCRYPT_2025_PRODUCTION
N8N_USER=admin
N8N_PASSWORD=K@rur0su24
N8N_API_KEY=tu_n8n_api_key_aqui

# =============================================================================
# CONFIGURACIÓN DE WHATSAPP API
# =============================================================================
WHATSAPP_API_KEY=tu_whatsapp_api_key_aqui
WHATSAPP_PHONE_NUMBER=tu_numero_whatsapp_aqui
WHATSAPP_WEBHOOK_URL=https://api.ccjap.echolab.xyz/api/waapi/webhook

# =============================================================================
# CONFIGURACIÓN DE APLICACIÓN
# =============================================================================
APP_URL=https://ccjap.echolab.xyz
ENABLE_WHATSAPP_NOTIFICATIONS=true
LOG_LEVEL=info

# =============================================================================
# CONFIGURACIÓN DE OPENAI (Opcional)
# =============================================================================
OPENAI_API_KEY=tu_openai_api_key_aqui

# =============================================================================
# CONFIGURACIÓN DE EMAIL (Opcional)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=tu_password_de_aplicacion
EMAIL_FROM=<EMAIL>

# =============================================================================
# CONFIGURACIÓN DE ALMACENAMIENTO (Opcional)
# =============================================================================
# Para almacenamiento en la nube (AWS S3, etc.)
# AWS_ACCESS_KEY_ID=tu_access_key
# AWS_SECRET_ACCESS_KEY=tu_secret_key
# AWS_REGION=us-east-1
# AWS_S3_BUCKET=ccjap-uploads

# =============================================================================
# CONFIGURACIÓN DE MONITOREO (Opcional)
# =============================================================================
# SENTRY_DSN=tu_sentry_dsn_aqui
# ANALYTICS_ID=tu_google_analytics_id
