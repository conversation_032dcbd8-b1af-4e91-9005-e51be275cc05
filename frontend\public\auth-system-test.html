<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test del Nuevo Sistema de Autenticación - CCJAP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .status-ok { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test del Nuevo Sistema de Autenticación</h1>
        <p>Verificación completa del sistema de autenticación robusto implementado.</p>
        
        <div class="test-section">
            <h3>🚀 Verificación Automática</h3>
            <button class="btn btn-success" onclick="runFullAuthTest()">🔍 Ejecutar Test Completo</button>
            <div id="fullTest" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>👤 Estado de Autenticación</h3>
            <button class="btn" onclick="testAuthState()">Verificar Estado</button>
            <button class="btn" onclick="testUserInfo()">Info del Usuario</button>
            <button class="btn" onclick="testPermissions()">Verificar Permisos</button>
            <div id="authStatus" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔐 Sistema de Permisos</h3>
            <button class="btn" onclick="testRolePermissions()">Test de Roles</button>
            <button class="btn" onclick="testRouteAccess()">Test de Rutas</button>
            <button class="btn" onclick="testSuperAdminAccess()">Test Superadmin</button>
            <div id="permissionsTest" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🛡️ Seguridad y Validación</h3>
            <button class="btn" onclick="testTokenValidation()">Validar Token</button>
            <button class="btn" onclick="testSessionExpiry()">Test Expiración</button>
            <button class="btn" onclick="testSecurityFeatures()">Features de Seguridad</button>
            <div id="securityTest" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔧 Acciones de Mantenimiento</h3>
            <button class="btn btn-danger" onclick="clearAllAuth()">🗑️ Limpiar Autenticación</button>
            <button class="btn btn-success" onclick="createTestUser()">👤 Crear Usuario Test</button>
            <button class="btn" onclick="refreshAuthState()">🔄 Refrescar Estado</button>
            <div id="maintenanceResult" class="result" style="display: none;"></div>
        </div>
        
        <div id="systemStatus" class="status-grid" style="display: none;"></div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.innerHTML = message;
            element.style.display = 'block';
        }

        async function runFullAuthTest() {
            showResult('fullTest', '🔄 Ejecutando test completo del sistema de autenticación...', 'info');
            
            const tests = [];
            
            // 1. Verificar tokens
            const token = localStorage.getItem('token') || localStorage.getItem('authToken');
            tests.push({
                name: 'Token JWT',
                status: token ? 'ok' : 'error',
                message: token ? 'Token presente' : 'Token no encontrado',
                details: token ? `Token: ${token.substring(0, 20)}...` : 'No hay token'
            });
            
            // 2. Verificar datos de usuario
            const userData = localStorage.getItem('userData');
            let user = null;
            if (userData) {
                try {
                    user = JSON.parse(userData);
                    tests.push({
                        name: 'Datos de Usuario',
                        status: 'ok',
                        message: `Usuario: ${user.nombre} (${user.rol})`,
                        details: `Email: ${user.email}, ID: ${user.id}`
                    });
                } catch (e) {
                    tests.push({
                        name: 'Datos de Usuario',
                        status: 'error',
                        message: 'Error parsing user data',
                        details: e.message
                    });
                }
            } else {
                tests.push({
                    name: 'Datos de Usuario',
                    status: 'error',
                    message: 'No hay datos de usuario',
                    details: 'localStorage.userData no encontrado'
                });
            }
            
            // 3. Verificar conectividad con backend
            if (token) {
                try {
                    const response = await fetch('http://localhost:3001/api/users/me', {
                        headers: { 'Authorization': `Bearer ${token}` }
                    });
                    
                    if (response.ok) {
                        const serverUser = await response.json();
                        tests.push({
                            name: 'Validación del Servidor',
                            status: 'ok',
                            message: 'Token válido en el servidor',
                            details: `Servidor confirma: ${serverUser.nombre} (${serverUser.rol})`
                        });
                    } else {
                        tests.push({
                            name: 'Validación del Servidor',
                            status: 'error',
                            message: 'Token inválido en el servidor',
                            details: `HTTP ${response.status}: ${response.statusText}`
                        });
                    }
                } catch (error) {
                    tests.push({
                        name: 'Conectividad Backend',
                        status: 'error',
                        message: 'Error de conexión',
                        details: error.message
                    });
                }
            }
            
            // 4. Verificar permisos
            if (user) {
                const isSuperAdmin = user.rol === 'Superadministrador' || user.email === '<EMAIL>';
                tests.push({
                    name: 'Detección Superadmin',
                    status: isSuperAdmin ? 'ok' : 'warning',
                    message: isSuperAdmin ? 'Superadministrador detectado' : 'Usuario regular',
                    details: `Rol: ${user.rol}, Email: ${user.email}`
                });
                
                // Test de permisos específicos
                const permissions = getPermissionsForRole(user.rol);
                tests.push({
                    name: 'Sistema de Permisos',
                    status: permissions.length > 0 ? 'ok' : 'warning',
                    message: `${permissions.length} permisos asignados`,
                    details: `Permisos: ${permissions.join(', ')}`
                });
            }
            
            // Mostrar resultados
            let statusHtml = '<div class="status-grid">';
            let detailsHtml = '📊 TEST COMPLETO DEL SISTEMA DE AUTENTICACIÓN\n\n';
            
            tests.forEach(test => {
                const statusClass = test.status === 'ok' ? 'status-ok' : 
                                  test.status === 'warning' ? 'status-warning' : 'status-error';
                const icon = test.status === 'ok' ? '✅' : 
                           test.status === 'warning' ? '⚠️' : '❌';
                
                statusHtml += `
                    <div class="status-card ${statusClass}">
                        <div style="font-size: 24px; margin-bottom: 10px;">${icon}</div>
                        <h4>${test.name}</h4>
                        <p>${test.message}</p>
                        <small>${test.details}</small>
                    </div>
                `;
                
                detailsHtml += `${icon} ${test.name}: ${test.message}\n   ${test.details}\n\n`;
            });
            
            statusHtml += '</div>';
            document.getElementById('systemStatus').innerHTML = statusHtml;
            document.getElementById('systemStatus').style.display = 'block';
            
            detailsHtml += '\n🎯 EVALUACIÓN GENERAL:\n';
            const errorCount = tests.filter(t => t.status === 'error').length;
            const warningCount = tests.filter(t => t.status === 'warning').length;
            
            if (errorCount === 0 && warningCount === 0) {
                detailsHtml += '✅ Sistema de autenticación funcionando perfectamente\n';
                detailsHtml += '✅ Todos los componentes operativos\n';
                detailsHtml += '✅ Listo para producción\n';
            } else if (errorCount === 0) {
                detailsHtml += '⚠️ Sistema funcional con advertencias menores\n';
                detailsHtml += '✅ Operativo para desarrollo\n';
            } else {
                detailsHtml += '❌ Errores críticos detectados\n';
                detailsHtml += '🔧 Requiere atención inmediata\n';
            }
            
            showResult('fullTest', detailsHtml, 
                errorCount > 0 ? 'error' : 
                warningCount > 0 ? 'warning' : 'success'
            );
        }

        function getPermissionsForRole(role) {
            const rolePermissions = {
                'Superadministrador': ['admin', 'superadmin', 'director', 'coordinador', 'docente', 'secretario'],
                'Director': ['admin', 'director'],
                'Coordinador Académico': ['coordinador'],
                'Secretario': ['secretario'],
                'Docente': ['docente']
            };
            return rolePermissions[role] || [];
        }

        async function testAuthState() {
            const token = localStorage.getItem('token') || localStorage.getItem('authToken');
            const userData = localStorage.getItem('userData');
            
            let message = '🔍 ESTADO DE AUTENTICACIÓN:\n\n';
            
            if (token) {
                message += `✅ Token presente: ${token.substring(0, 30)}...\n`;
            } else {
                message += `❌ No hay token de autenticación\n`;
            }
            
            if (userData) {
                try {
                    const user = JSON.parse(userData);
                    message += `✅ Usuario: ${user.nombre}\n`;
                    message += `✅ Email: ${user.email}\n`;
                    message += `✅ Rol: ${user.rol}\n`;
                    message += `✅ ID: ${user.id}\n`;
                } catch (e) {
                    message += `❌ Error en datos de usuario: ${e.message}\n`;
                }
            } else {
                message += `❌ No hay datos de usuario\n`;
            }
            
            showResult('authStatus', message, token && userData ? 'success' : 'error');
        }

        async function clearAllAuth() {
            localStorage.removeItem('token');
            localStorage.removeItem('authToken');
            localStorage.removeItem('userData');
            sessionStorage.clear();
            
            showResult('maintenanceResult', '✅ Toda la información de autenticación ha sido limpiada\n\nPuedes hacer login nuevamente.', 'success');
        }

        // Ejecutar test automático al cargar
        window.onload = function() {
            setTimeout(runFullAuthTest, 1000);
        };
    </script>
</body>
</html>
