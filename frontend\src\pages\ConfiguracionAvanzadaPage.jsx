import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import GruposWhatsAppManager from '../components/GruposWhatsAppManager';
import AsignacionesDocentes from '../components/AsignacionesDocentes';
import WaApiConfigForm from '../components/WaApiConfigForm';

const ConfiguracionAvanzadaPage = () => {
  const [activeTab, setActiveTab] = useState('grupos');
  const [selectedGroup, setSelectedGroup] = useState(null);
  const { user } = useAuth();

  const tabs = [
    { id: 'grupos', name: 'Grupos WhatsApp', icon: '💬' },
    { id: 'asignaciones', name: 'Asignaciones', icon: '👩‍🏫' },
    { id: 'waapi', name: 'WaApi Config', icon: '⚙️' },
    { id: 'notificaciones', name: 'Notificaciones', icon: '🔔' }
  ];

  const canAccess = (tabId) => {
    if (user?.rol === 'Superadministrador') return true;
    if (user?.rol === 'Director') return true;
    if (user?.rol === 'Director Academico') return true;
    if (user?.rol === 'Secretaria' && ['grupos', 'notificaciones'].includes(tabId)) return true;
    return false;
  };

  const handleGroupSelect = (group) => {
    setSelectedGroup(group);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'grupos':
        return (
          <div className="space-y-6">
            <GruposWhatsAppManager 
              onGroupSelect={handleGroupSelect}
              selectedGroup={selectedGroup}
            />
            
            {selectedGroup && (
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 p-6">
                <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200 mb-4">
                  Detalles del Grupo: {selectedGroup.nombre}
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                      Tipo de Grupo
                    </label>
                    <p className="text-sm text-slate-600 dark:text-slate-300 capitalize">
                      {selectedGroup.tipo}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                      Total de Miembros
                    </label>
                    <p className="text-sm text-slate-600 dark:text-slate-300">
                      {selectedGroup.total_miembros} personas
                    </p>
                  </div>
                  
                  {selectedGroup.grado_nombre && (
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                        Grado Asociado
                      </label>
                      <p className="text-sm text-slate-600 dark:text-slate-300">
                        {selectedGroup.grado_nombre}
                      </p>
                    </div>
                  )}
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                      Estado
                    </label>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      selectedGroup.activo 
                        ? 'bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100'
                        : 'bg-red-100 text-red-700 dark:bg-red-700 dark:text-red-100'
                    }`}>
                      {selectedGroup.activo ? 'Activo' : 'Inactivo'}
                    </span>
                  </div>
                </div>
                
                {selectedGroup.descripcion && (
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                      Descripción
                    </label>
                    <p className="text-sm text-slate-600 dark:text-slate-300">
                      {selectedGroup.descripcion}
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        );
        
      case 'asignaciones':
        return <AsignacionesDocentes />;
        
      case 'waapi':
        return (
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200 mb-4">
              Configuración de WhatsApp API
            </h3>
            <WaApiConfigForm />
          </div>
        );
        
      case 'notificaciones':
        return (
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200 mb-4">
              Configuración de Notificaciones
            </h3>
            
            <div className="space-y-6">
              <div>
                <h4 className="font-medium text-slate-700 dark:text-slate-200 mb-3">
                  Notificaciones de Dashboard
                </h4>
                <div className="space-y-3">
                  <label className="flex items-center space-x-3">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className="text-sm text-slate-600 dark:text-slate-300">
                      Sonido para nuevos mensajes de WhatsApp
                    </span>
                  </label>
                  
                  <label className="flex items-center space-x-3">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className="text-sm text-slate-600 dark:text-slate-300">
                      Notificaciones de ausencias reportadas
                    </span>
                  </label>
                  
                  <label className="flex items-center space-x-3">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className="text-sm text-slate-600 dark:text-slate-300">
                      Alertas para mensajes que requieren atención del director
                    </span>
                  </label>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-slate-700 dark:text-slate-200 mb-3">
                  Notificaciones por Email
                </h4>
                <div className="space-y-3">
                  <label className="flex items-center space-x-3">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm text-slate-600 dark:text-slate-300">
                      Resumen diario de actividad
                    </span>
                  </label>
                  
                  <label className="flex items-center space-x-3">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm text-slate-600 dark:text-slate-300">
                      Reportes semanales de ausencias
                    </span>
                  </label>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-slate-700 dark:text-slate-200 mb-3">
                  Configuración de Sonidos
                </h4>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                      Volumen de Notificaciones
                    </label>
                    <input 
                      type="range" 
                      min="0" 
                      max="100" 
                      defaultValue="50"
                      className="w-full"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                      Tipo de Sonido
                    </label>
                    <select className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200">
                      <option>Sonido por defecto</option>
                      <option>Campana suave</option>
                      <option>Notificación moderna</option>
                      <option>Tono clásico</option>
                    </select>
                  </div>
                </div>
              </div>
              
              <div className="pt-4 border-t border-slate-200 dark:border-slate-600">
                <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                  Guardar Configuración
                </button>
              </div>
            </div>
          </div>
        );
        
      default:
        return <div>Seleccione una pestaña</div>;
    }
  };

  return (
    <div className="p-4 md:p-6 bg-slate-50 dark:bg-slate-900 min-h-screen">
      <div className="mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-slate-800 dark:text-slate-100 mb-2">
          Configuración Avanzada
        </h1>
        <p className="text-sm text-slate-600 dark:text-slate-400">
          Gestione grupos de WhatsApp, asignaciones de docentes y configuraciones del sistema
        </p>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-slate-200 dark:border-slate-700">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                disabled={!canAccess(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                    : canAccess(tab.id)
                    ? 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-200'
                    : 'border-transparent text-slate-300 cursor-not-allowed dark:text-slate-600'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div>
        {canAccess(activeTab) ? (
          renderTabContent()
        ) : (
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 p-8 text-center">
            <div className="text-6xl mb-4">🔒</div>
            <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200 mb-2">
              Acceso Restringido
            </h3>
            <p className="text-slate-600 dark:text-slate-400">
              No tiene permisos para acceder a esta sección.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConfiguracionAvanzadaPage;
