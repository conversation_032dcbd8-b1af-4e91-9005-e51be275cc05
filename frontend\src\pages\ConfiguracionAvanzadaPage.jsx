import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import GruposWhatsAppManager from '../components/GruposWhatsAppManager';
import AsignacionesDocentes from '../components/AsignacionesDocentes';
import WaApiConfigForm from '../components/WaApiConfigForm';

const ConfiguracionAvanzadaPage = () => {
  const [activeTab, setActiveTab] = useState('grupos');
  const [selectedGroup, setSelectedGroup] = useState(null);
  const { user } = useAuth();

  const tabs = [
    { id: 'grupos', name: 'Grupos WhatsApp', icon: '💬' },
    { id: 'asignaciones', name: 'Asignaciones', icon: '👩‍🏫' },
    { id: 'waapi', name: 'WaApi Config', icon: '⚙️' },
    { id: 'n8n', name: 'n8n Config', icon: '🔄' },
    { id: 'database', name: 'Base de Datos', icon: '🗄️' },
    { id: 'system', name: '<PERSON><PERSON><PERSON>', icon: '🖥️' },
    { id: 'notificaciones', name: 'Notificaciones', icon: '🔔' }
  ];

  const canAccess = (tabId) => {
    console.log('🔍 Verificando acceso:', {
      tabId,
      userRole: user?.rol,
      userEmail: user?.email,
      user: user
    });

    // Superadministrador tiene acceso COMPLETO a TODO
    if (user?.rol === 'Superadministrador') {
      console.log('✅ Acceso concedido: Superadministrador tiene acceso completo');
      return true;
    }

    // Director NO tiene acceso a configuraciones avanzadas (solo configuración básica)
    if (user?.rol === 'Director') {
      console.log('❌ Acceso denegado: Director no tiene acceso a configuración avanzada');
      return false;
    }

    // Coordinador Académico tiene acceso limitado
    if (user?.rol === 'Coordinador Académico' && ['grupos', 'asignaciones'].includes(tabId)) {
      console.log('✅ Acceso concedido: Coordinador Académico tiene acceso limitado');
      return true;
    }

    // Secretario tiene acceso muy limitado
    if (user?.rol === 'Secretario' && ['grupos'].includes(tabId)) {
      console.log('✅ Acceso concedido: Secretario tiene acceso muy limitado');
      return true;
    }

    console.log('❌ Acceso denegado: No se cumple ninguna condición');
    return false;
  };

  const handleGroupSelect = (group) => {
    setSelectedGroup(group);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'grupos':
        return (
          <div className="space-y-6">
            <GruposWhatsAppManager 
              onGroupSelect={handleGroupSelect}
              selectedGroup={selectedGroup}
            />
            
            {selectedGroup && (
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 p-6">
                <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200 mb-4">
                  Detalles del Grupo: {selectedGroup.nombre}
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                      Tipo de Grupo
                    </label>
                    <p className="text-sm text-slate-600 dark:text-slate-300 capitalize">
                      {selectedGroup.tipo}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                      Total de Miembros
                    </label>
                    <p className="text-sm text-slate-600 dark:text-slate-300">
                      {selectedGroup.total_miembros} personas
                    </p>
                  </div>
                  
                  {selectedGroup.grado_nombre && (
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                        Grado Asociado
                      </label>
                      <p className="text-sm text-slate-600 dark:text-slate-300">
                        {selectedGroup.grado_nombre}
                      </p>
                    </div>
                  )}
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                      Estado
                    </label>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      selectedGroup.activo 
                        ? 'bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100'
                        : 'bg-red-100 text-red-700 dark:bg-red-700 dark:text-red-100'
                    }`}>
                      {selectedGroup.activo ? 'Activo' : 'Inactivo'}
                    </span>
                  </div>
                </div>
                
                {selectedGroup.descripcion && (
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                      Descripción
                    </label>
                    <p className="text-sm text-slate-600 dark:text-slate-300">
                      {selectedGroup.descripcion}
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        );
        
      case 'asignaciones':
        return <AsignacionesDocentes />;
        
      case 'waapi':
        return (
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200 mb-4">
              Configuración de WhatsApp API
            </h3>
            <WaApiConfigForm />
          </div>
        );

      case 'n8n':
        return (
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200 mb-4">
              Configuración de n8n
            </h3>
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-2">
                    URL de n8n
                  </label>
                  <input
                    type="url"
                    defaultValue="http://localhost:5678"
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
                    placeholder="http://localhost:5678"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-2">
                    API Key de n8n
                  </label>
                  <input
                    type="password"
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
                    placeholder="Ingrese su API Key de n8n"
                  />
                </div>
              </div>

              <div>
                <h4 className="font-medium text-slate-700 dark:text-slate-200 mb-3">
                  Estado de Conexión
                </h4>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-slate-600 dark:text-slate-300">
                    Conectado a n8n
                  </span>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-slate-700 dark:text-slate-200 mb-3">
                  Workflows Activos
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                    <span className="text-sm text-slate-700 dark:text-slate-200">Análisis de Mensajes WhatsApp</span>
                    <span className="px-2 py-1 bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100 rounded-full text-xs">Activo</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                    <span className="text-sm text-slate-700 dark:text-slate-200">Notificaciones Automáticas</span>
                    <span className="px-2 py-1 bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100 rounded-full text-xs">Activo</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                    <span className="text-sm text-slate-700 dark:text-slate-200">Reportes de Ausencias</span>
                    <span className="px-2 py-1 bg-yellow-100 text-yellow-700 dark:bg-yellow-700 dark:text-yellow-100 rounded-full text-xs">Pausado</span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                  Probar Conexión
                </button>
                <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                  Abrir n8n
                </button>
              </div>
            </div>
          </div>
        );

      case 'database':
        return (
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200 mb-4">
              Gestión de Base de Datos
            </h3>
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                  <h4 className="font-medium text-slate-700 dark:text-slate-200 mb-2">Usuarios</h4>
                  <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">127</p>
                  <p className="text-xs text-slate-500 dark:text-slate-400">Total registrados</p>
                </div>
                <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                  <h4 className="font-medium text-slate-700 dark:text-slate-200 mb-2">Alumnos</h4>
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">1,234</p>
                  <p className="text-xs text-slate-500 dark:text-slate-400">Estudiantes activos</p>
                </div>
                <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                  <h4 className="font-medium text-slate-700 dark:text-slate-200 mb-2">Mensajes</h4>
                  <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">5,678</p>
                  <p className="text-xs text-slate-500 dark:text-slate-400">Procesados este mes</p>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-slate-700 dark:text-slate-200 mb-3">
                  Operaciones de Mantenimiento
                </h4>
                <div className="space-y-3">
                  <button className="w-full text-left p-3 bg-slate-50 dark:bg-slate-700 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-slate-700 dark:text-slate-200">Backup de Base de Datos</span>
                      <span className="text-xs text-slate-500 dark:text-slate-400">Último: Hace 2 horas</span>
                    </div>
                  </button>
                  <button className="w-full text-left p-3 bg-slate-50 dark:bg-slate-700 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-slate-700 dark:text-slate-200">Limpiar Logs Antiguos</span>
                      <span className="text-xs text-slate-500 dark:text-slate-400">Automático cada semana</span>
                    </div>
                  </button>
                  <button className="w-full text-left p-3 bg-slate-50 dark:bg-slate-700 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-slate-700 dark:text-slate-200">Optimizar Tablas</span>
                      <span className="text-xs text-slate-500 dark:text-slate-400">Recomendado mensualmente</span>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        );

      case 'system':
        return (
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200 mb-4">
              Configuración del Sistema
            </h3>
            <div className="space-y-6">
              <div>
                <h4 className="font-medium text-slate-700 dark:text-slate-200 mb-3">
                  Variables de Entorno
                </h4>
                <div className="space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                        JWT_SECRET
                      </label>
                      <input
                        type="password"
                        defaultValue="K@rur0su24_JWT_SECRET_2025"
                        className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                        JWT_EXPIRES_IN
                      </label>
                      <input
                        type="text"
                        defaultValue="24h"
                        className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-slate-700 dark:text-slate-200 mb-3">
                  Estado del Sistema
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-slate-600 dark:text-slate-300">Backend API</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-slate-600 dark:text-slate-300">Base de Datos</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-slate-600 dark:text-slate-300">WebSocket</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span className="text-sm text-slate-600 dark:text-slate-300">n8n Service</span>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t border-slate-200 dark:border-slate-600">
                <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors mr-3">
                  Guardar Configuración
                </button>
                <button className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                  Reiniciar Sistema
                </button>
              </div>
            </div>
          </div>
        );
        
      case 'notificaciones':
        return (
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200 mb-4">
              Configuración de Notificaciones
            </h3>
            
            <div className="space-y-6">
              <div>
                <h4 className="font-medium text-slate-700 dark:text-slate-200 mb-3">
                  Notificaciones de Dashboard
                </h4>
                <div className="space-y-3">
                  <label className="flex items-center space-x-3">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className="text-sm text-slate-600 dark:text-slate-300">
                      Sonido para nuevos mensajes de WhatsApp
                    </span>
                  </label>
                  
                  <label className="flex items-center space-x-3">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className="text-sm text-slate-600 dark:text-slate-300">
                      Notificaciones de ausencias reportadas
                    </span>
                  </label>
                  
                  <label className="flex items-center space-x-3">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className="text-sm text-slate-600 dark:text-slate-300">
                      Alertas para mensajes que requieren atención del director
                    </span>
                  </label>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-slate-700 dark:text-slate-200 mb-3">
                  Notificaciones por Email
                </h4>
                <div className="space-y-3">
                  <label className="flex items-center space-x-3">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm text-slate-600 dark:text-slate-300">
                      Resumen diario de actividad
                    </span>
                  </label>
                  
                  <label className="flex items-center space-x-3">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm text-slate-600 dark:text-slate-300">
                      Reportes semanales de ausencias
                    </span>
                  </label>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-slate-700 dark:text-slate-200 mb-3">
                  Configuración de Sonidos
                </h4>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                      Volumen de Notificaciones
                    </label>
                    <input 
                      type="range" 
                      min="0" 
                      max="100" 
                      defaultValue="50"
                      className="w-full"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                      Tipo de Sonido
                    </label>
                    <select className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200">
                      <option>Sonido por defecto</option>
                      <option>Campana suave</option>
                      <option>Notificación moderna</option>
                      <option>Tono clásico</option>
                    </select>
                  </div>
                </div>
              </div>
              
              <div className="pt-4 border-t border-slate-200 dark:border-slate-600">
                <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                  Guardar Configuración
                </button>
              </div>
            </div>
          </div>
        );
        
      default:
        return <div>Seleccione una pestaña</div>;
    }
  };

  return (
    <div className="p-4 md:p-6 bg-slate-50 dark:bg-slate-900 min-h-screen">
      <div className="mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-slate-800 dark:text-slate-100 mb-2">
          Configuración Avanzada
        </h1>
        <p className="text-sm text-slate-600 dark:text-slate-400">
          Gestione grupos de WhatsApp, asignaciones de docentes y configuraciones del sistema
        </p>

        {/* Panel de Debug Temporal */}
        {user?.rol === 'Superadministrador' && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="text-sm font-medium text-blue-800 mb-2">🔧 Debug Info (Solo Superadmin)</h4>
            <div className="text-xs text-blue-700 space-y-1">
              <p><strong>Usuario:</strong> {user?.nombre} ({user?.email})</p>
              <p><strong>Rol:</strong> {user?.rol}</p>
              <p><strong>Tab Activo:</strong> {activeTab}</p>
              <p><strong>Acceso al Tab:</strong> {canAccess(activeTab) ? '✅ Permitido' : '❌ Denegado'}</p>
            </div>
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-slate-200 dark:border-slate-700">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                disabled={!canAccess(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                    : canAccess(tab.id)
                    ? 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-200'
                    : 'border-transparent text-slate-300 cursor-not-allowed dark:text-slate-600'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div>
        {canAccess(activeTab) ? (
          renderTabContent()
        ) : (
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 p-8 text-center">
            <div className="text-6xl mb-4">🔒</div>
            <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200 mb-2">
              Acceso Restringido
            </h3>
            <p className="text-slate-600 dark:text-slate-400">
              No tiene permisos para acceder a esta sección.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConfiguracionAvanzadaPage;
