import React, { createContext, useState, useEffect, useContext, useCallback } from 'react';

// Definir la estructura del usuario
const defaultUser = {
  id: null,
  nombre: '',
  email: '',
  rol: '',
  institucion_id: null
};

// Crear el contexto con valores por defecto
const AuthContext = createContext({
  user: null,
  currentUser: null,
  token: null,
  isAuthenticated: false,
  isLoading: true,
  login: () => {},
  logout: () => {},
  updateUser: () => {},
  refreshToken: () => {}
});

// Hook personalizado para usar el contexto de autenticación
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth debe ser usado dentro de un AuthProvider');
  }
  return context;
};

// Proveedor de autenticación
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Función para limpiar el almacenamiento
  const clearStorage = useCallback(() => {
    localStorage.removeItem('token');
    localStorage.removeItem('authToken');
    localStorage.removeItem('userData');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('authToken');
    sessionStorage.removeItem('userData');
  }, []);

  // Función para validar el token
  const validateToken = useCallback(async (tokenToValidate) => {
    try {
      const response = await fetch('http://localhost:3001/api/users/me', {
        headers: {
          'Authorization': `Bearer ${tokenToValidate}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const userData = await response.json();
        return userData;
      } else {
        throw new Error('Token inválido');
      }
    } catch (error) {
      console.error('Error validating token:', error);
      return null;
    }
  }, []);

  // Función de login
  const login = useCallback(async (newToken, userData) => {
    try {
      // Validar que los datos sean correctos
      if (!newToken || !userData) {
        throw new Error('Token o datos de usuario inválidos');
      }

      // Guardar en localStorage
      localStorage.setItem('token', newToken);
      localStorage.setItem('authToken', newToken); // Compatibilidad
      localStorage.setItem('userData', JSON.stringify(userData));

      // Actualizar estado
      setToken(newToken);
      setUser(userData);

      console.log('✅ Login exitoso:', userData);
      return true;
    } catch (error) {
      console.error('❌ Error en login:', error);
      clearStorage();
      return false;
    }
  }, [clearStorage]);

  // Función de logout
  const logout = useCallback(() => {
    clearStorage();
    setToken(null);
    setUser(null);
    console.log('✅ Logout exitoso');
  }, [clearStorage]);

  // Función para actualizar usuario
  const updateUser = useCallback((userData) => {
    if (userData) {
      localStorage.setItem('userData', JSON.stringify(userData));
      setUser(userData);
      console.log('✅ Usuario actualizado:', userData);
    }
  }, []);

  // Función para refrescar token
  const refreshToken = useCallback(async () => {
    const currentToken = token || localStorage.getItem('token');
    if (currentToken) {
      const userData = await validateToken(currentToken);
      if (userData) {
        setUser(userData);
        return true;
      } else {
        logout();
        return false;
      }
    }
    return false;
  }, [token, validateToken, logout]);

  // Inicialización del contexto
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Buscar token en localStorage
        const storedToken = localStorage.getItem('token') || localStorage.getItem('authToken');
        const storedUserData = localStorage.getItem('userData');

        if (storedToken && storedUserData) {
          try {
            const userData = JSON.parse(storedUserData);

            // Validar token con el servidor
            const validatedUser = await validateToken(storedToken);

            if (validatedUser) {
              setToken(storedToken);
              setUser(validatedUser);
              console.log('✅ Sesión restaurada:', validatedUser);
            } else {
              // Token inválido, limpiar
              clearStorage();
              console.log('❌ Token inválido, sesión limpiada');
            }
          } catch (error) {
            console.error('❌ Error parsing stored data:', error);
            clearStorage();
          }
        }
      } catch (error) {
        console.error('❌ Error initializing auth:', error);
        clearStorage();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, [validateToken, clearStorage]);

  // Valor del contexto
  const contextValue = {
    user,
    currentUser: user, // Alias para compatibilidad
    token,
    isAuthenticated: !!(token && user),
    isLoading,
    login,
    logout,
    updateUser,
    refreshToken
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Exportar por defecto el hook
export default useAuth;

// NO exportar AuthContext directamente para evitar uso incorrecto
// Solo usar a través del hook useAuth()
