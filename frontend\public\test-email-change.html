<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de Cambio de Email - CCJAP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Prueba de Funcionalidad de Cambio de Email</h1>
        <p>Esta herramienta permite probar las nuevas funcionalidades de cambio de email.</p>
        
        <div class="test-section">
            <h3>1. Verificar Disponibilidad de Email</h3>
            <input type="email" id="emailToCheck" placeholder="<EMAIL>" />
            <button class="btn" onclick="checkEmailAvailability()">Verificar Disponibilidad</button>
            <div id="checkResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Información del Usuario Actual</h3>
            <button class="btn" onclick="getCurrentUser()">Obtener Usuario Actual</button>
            <div id="userResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Simular Cambio de Email</h3>
            <input type="email" id="newEmail" placeholder="<EMAIL>" />
            <button class="btn" onclick="simulateEmailChange()">Simular Cambio</button>
            <div id="changeResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>4. Instrucciones de Uso</h3>
            <ol>
                <li>Primero, asegúrate de estar logueado en el sistema</li>
                <li>Usa la herramienta de verificación para probar emails</li>
                <li>Ve al perfil del usuario para cambiar el email real</li>
                <li>Observa las validaciones en tiempo real</li>
            </ol>
            
            <h4>Características Implementadas:</h4>
            <ul>
                <li>✅ Validación de formato de email en tiempo real</li>
                <li>✅ Verificación de disponibilidad del email</li>
                <li>✅ Confirmación antes de cambiar el email</li>
                <li>✅ Indicadores visuales de estado</li>
                <li>✅ Mensajes informativos sobre las implicaciones</li>
                <li>✅ Manejo mejorado de errores</li>
                <li>✅ Notificaciones específicas para cambio de email</li>
            </ul>
        </div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        async function checkEmailAvailability() {
            const email = document.getElementById('emailToCheck').value;
            
            if (!email) {
                showResult('checkResult', 'Por favor ingresa un email', 'error');
                return;
            }

            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    showResult('checkResult', 'No hay token de autenticación. Por favor inicia sesión.', 'error');
                    return;
                }

                const response = await fetch(`http://localhost:3001/api/users/check-email?email=${encodeURIComponent(email)}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    const message = `Email: ${data.email}\nDisponible: ${data.available ? 'SÍ ✅' : 'NO ❌'}\nMensaje: ${data.message}`;
                    showResult('checkResult', message, data.available ? 'success' : 'error');
                } else {
                    showResult('checkResult', `Error: ${data.error}`, 'error');
                }
            } catch (error) {
                showResult('checkResult', `Error de conexión: ${error.message}`, 'error');
            }
        }

        async function getCurrentUser() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    showResult('userResult', 'No hay token de autenticación. Por favor inicia sesión.', 'error');
                    return;
                }

                const response = await fetch('http://localhost:3001/api/users/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    const message = `ID: ${data.id}\nNombre: ${data.nombre}\nEmail: ${data.email}\nRol: ${data.rol}\nInstitución ID: ${data.institucion_id || 'N/A'}`;
                    showResult('userResult', message, 'success');
                } else {
                    showResult('userResult', `Error: ${data.error}`, 'error');
                }
            } catch (error) {
                showResult('userResult', `Error de conexión: ${error.message}`, 'error');
            }
        }

        function simulateEmailChange() {
            const newEmail = document.getElementById('newEmail').value;
            
            if (!newEmail) {
                showResult('changeResult', 'Por favor ingresa un nuevo email', 'error');
                return;
            }

            const message = `Simulación de cambio de email:\n\n` +
                           `1. Se validaría el formato: ${/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newEmail) ? '✅ Válido' : '❌ Inválido'}\n` +
                           `2. Se verificaría disponibilidad\n` +
                           `3. Se mostraría confirmación\n` +
                           `4. Se actualizaría en la base de datos\n` +
                           `5. Se notificaría al usuario\n\n` +
                           `Para hacer el cambio real, ve a la página de perfil del usuario.`;
            
            showResult('changeResult', message, 'info');
        }

        // Verificar si hay token al cargar la página
        window.onload = function() {
            const token = localStorage.getItem('token');
            if (!token) {
                document.body.insertAdjacentHTML('afterbegin', 
                    '<div style="background: #f8d7da; color: #721c24; padding: 15px; margin-bottom: 20px; border-radius: 5px; text-align: center;">' +
                    '⚠️ No hay token de autenticación. <a href="/" style="color: #721c24;">Ir al Login</a>' +
                    '</div>'
                );
            }
        };
    </script>
</body>
</html>
