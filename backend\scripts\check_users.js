const db = require('../config/db');

async function checkUsers() {
  try {
    const result = await db.query('SELECT email, rol, nombre FROM usuarios');
    console.log('Usuarios en la base de datos:');
    result.rows.forEach(user => {
      console.log(`- ${user.email} (${user.rol}) - ${user.nombre}`);
    });
  } catch (error) {
    console.error('Error:', error);
  }
}

checkUsers().then(() => process.exit(0));
