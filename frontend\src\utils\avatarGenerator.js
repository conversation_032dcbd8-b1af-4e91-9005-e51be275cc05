import { createAvatar } from '@dicebear/core';
import { avataaars, personas, initials, lorelei, micah } from '@dicebear/collection';

// Estilos disponibles para avatares
const avatarStyles = [
  { style: avataaars, name: 'avataaars' },
  { style: personas, name: 'personas' },
  { style: initials, name: 'initials' },
  { style: lorel<PERSON>, name: 'lorel<PERSON>' },
  { style: micah, name: 'micah' }
];

// Colores de fondo disponibles
const backgroundColors = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
  '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
];

/**
 * Genera un avatar aleatorio basado en un seed (nombre o email)
 * @param {string} seed - Texto base para generar el avatar (nombre, email, etc.)
 * @param {string} style - Estilo específico del avatar (opcional)
 * @returns {string} - SVG del avatar como string
 */
export const generateRandomAvatar = (seed, style = null) => {
  try {
    // Seleccionar estilo aleatorio si no se especifica
    const selectedStyle = style 
      ? avatarStyles.find(s => s.name === style)?.style || avataaars
      : avatarStyles[Math.floor(Math.random() * avatarStyles.length)].style;

    // Seleccionar color de fondo aleatorio
    const backgroundColor = backgroundColors[Math.floor(Math.random() * backgroundColors.length)];

    // Configuración base del avatar
    const avatarConfig = {
      seed: seed || Math.random().toString(36).substring(7),
      backgroundColor: [backgroundColor],
      size: 200,
    };

    // Configuraciones específicas por estilo
    if (selectedStyle === initials) {
      avatarConfig.radius = 50;
      avatarConfig.fontSize = 60;
    } else if (selectedStyle === avataaars) {
      avatarConfig.accessories = ['round', 'wayfarers', 'sunglasses'];
      avatarConfig.clothing = ['blazer', 'sweater', 'shirt'];
      avatarConfig.eyes = ['happy', 'default', 'wink'];
      avatarConfig.mouth = ['smile', 'default', 'twinkle'];
    }

    // Crear el avatar
    const avatar = createAvatar(selectedStyle, avatarConfig);
    
    return avatar.toString();
  } catch (error) {
    console.error('Error generando avatar:', error);
    // Fallback: avatar simple con iniciales
    return generateFallbackAvatar(seed);
  }
};

/**
 * Genera un avatar de respaldo simple con iniciales
 * @param {string} seed - Texto base
 * @returns {string} - SVG simple
 */
const generateFallbackAvatar = (seed) => {
  const initials = getInitials(seed);
  const color = backgroundColors[Math.floor(Math.random() * backgroundColors.length)];
  
  return `
    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="100" fill="${color}"/>
      <text x="100" y="120" font-family="Arial, sans-serif" font-size="60" 
            font-weight="bold" text-anchor="middle" fill="white">
        ${initials}
      </text>
    </svg>
  `;
};

/**
 * Extrae las iniciales de un nombre o email
 * @param {string} text - Texto del cual extraer iniciales
 * @returns {string} - Iniciales (máximo 2 caracteres)
 */
const getInitials = (text) => {
  if (!text) return 'U';
  
  const words = text.split(/[\s@.]+/).filter(word => word.length > 0);
  if (words.length === 0) return 'U';
  
  if (words.length === 1) {
    return words[0].substring(0, 2).toUpperCase();
  }
  
  return (words[0][0] + words[1][0]).toUpperCase();
};

/**
 * Convierte un SVG string a Data URL
 * @param {string} svgString - SVG como string
 * @returns {string} - Data URL del SVG
 */
export const svgToDataUrl = (svgString) => {
  const base64 = btoa(unescape(encodeURIComponent(svgString)));
  return `data:image/svg+xml;base64,${base64}`;
};

/**
 * Genera un avatar y lo convierte a Data URL
 * @param {string} seed - Texto base
 * @param {string} style - Estilo del avatar
 * @returns {string} - Data URL del avatar
 */
export const generateAvatarDataUrl = (seed, style = null) => {
  const svg = generateRandomAvatar(seed, style);
  return svgToDataUrl(svg);
};

/**
 * Lista de estilos disponibles
 */
export const getAvailableStyles = () => {
  return avatarStyles.map(style => style.name);
};

/**
 * Genera múltiples avatares para selección
 * @param {string} seed - Texto base
 * @param {number} count - Cantidad de avatares a generar
 * @returns {Array} - Array de Data URLs
 */
export const generateMultipleAvatars = (seed, count = 5) => {
  const avatars = [];
  const styles = getAvailableStyles();
  
  for (let i = 0; i < count; i++) {
    const style = styles[i % styles.length];
    const modifiedSeed = `${seed}-${i}`;
    avatars.push({
      id: i,
      style: style,
      dataUrl: generateAvatarDataUrl(modifiedSeed, style)
    });
  }
  
  return avatars;
};
