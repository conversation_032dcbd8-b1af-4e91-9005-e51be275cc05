// Fallback avatar generator without external dependencies
// import { createAvatar } from '@dicebear/core';
// import { avataaars, personas, initials, lorelei, micah } from '@dicebear/collection';

// Estilos disponibles para avatares (fallback)
const avatarStyles = [
  { name: 'geometric' },
  { name: 'abstract' },
  { name: 'initials' },
  { name: 'pattern' },
  { name: 'gradient' }
];

// Colores de fondo disponibles
const backgroundColors = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
  '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
];

/**
 * Genera un avatar aleatorio basado en un seed (nombre o email)
 * @param {string} seed - Texto base para generar el avatar (nombre, email, etc.)
 * @param {string} style - Estilo específico del avatar (opcional)
 * @returns {string} - SVG del avatar como string
 */
export const generateRandomAvatar = (seed, style = null) => {
  try {
    // Usar el generador fallback por ahora
    return generateFallbackAvatar(seed, style);
  } catch (error) {
    console.error('Error generando avatar:', error);
    // Fallback: avatar simple con iniciales
    return generateFallbackAvatar(seed);
  }
};

/**
 * Genera un avatar de respaldo simple con iniciales
 * @param {string} seed - Texto base
 * @param {string} style - Estilo del avatar
 * @returns {string} - SVG simple
 */
const generateFallbackAvatar = (seed, style = 'initials') => {
  const initials = getInitials(seed);
  const color = backgroundColors[Math.floor(Math.random() * backgroundColors.length)];
  const secondaryColor = backgroundColors[Math.floor(Math.random() * backgroundColors.length)];

  // Generar diferentes estilos de avatar
  switch (style) {
    case 'geometric':
      return generateGeometricAvatar(seed, color, secondaryColor);
    case 'abstract':
      return generateAbstractAvatar(seed, color, secondaryColor);
    case 'pattern':
      return generatePatternAvatar(seed, color, secondaryColor);
    case 'gradient':
      return generateGradientAvatar(seed, initials, color, secondaryColor);
    default:
      return generateInitialsAvatar(initials, color);
  }
};

/**
 * Genera avatar con iniciales
 */
const generateInitialsAvatar = (initials, color) => {
  return `
    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="100" fill="${color}"/>
      <text x="100" y="120" font-family="Arial, sans-serif" font-size="60"
            font-weight="bold" text-anchor="middle" fill="white">
        ${initials}
      </text>
    </svg>
  `;
};

/**
 * Genera avatar geométrico
 */
const generateGeometricAvatar = (seed, color1, color2) => {
  const hash = simpleHash(seed);
  const shapes = [];

  for (let i = 0; i < 5; i++) {
    const x = (hash * (i + 1)) % 150 + 25;
    const y = (hash * (i + 2)) % 150 + 25;
    const size = (hash * (i + 3)) % 30 + 10;
    const shapeColor = i % 2 === 0 ? color1 : color2;

    if (i % 3 === 0) {
      shapes.push(`<circle cx="${x}" cy="${y}" r="${size}" fill="${shapeColor}" opacity="0.8"/>`);
    } else if (i % 3 === 1) {
      shapes.push(`<rect x="${x-size}" y="${y-size}" width="${size*2}" height="${size*2}" fill="${shapeColor}" opacity="0.8"/>`);
    } else {
      shapes.push(`<polygon points="${x},${y-size} ${x+size},${y+size} ${x-size},${y+size}" fill="${shapeColor}" opacity="0.8"/>`);
    }
  }

  return `
    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="200" fill="#f0f0f0"/>
      ${shapes.join('')}
    </svg>
  `;
};

/**
 * Genera avatar abstracto
 */
const generateAbstractAvatar = (seed, color1, color2) => {
  const hash = simpleHash(seed);
  const paths = [];

  for (let i = 0; i < 3; i++) {
    const x1 = (hash * (i + 1)) % 200;
    const y1 = (hash * (i + 2)) % 200;
    const x2 = (hash * (i + 3)) % 200;
    const y2 = (hash * (i + 4)) % 200;
    const color = i % 2 === 0 ? color1 : color2;

    paths.push(`<path d="M${x1},${y1} Q${x2},${y2} ${(x1+x2)/2},${(y1+y2)/2}" stroke="${color}" stroke-width="20" fill="none" opacity="0.7"/>`);
  }

  return `
    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="200" fill="#ffffff"/>
      ${paths.join('')}
    </svg>
  `;
};

/**
 * Genera avatar con patrón
 */
const generatePatternAvatar = (seed, color1, color2) => {
  const hash = simpleHash(seed);
  const pattern = [];

  for (let x = 0; x < 200; x += 20) {
    for (let y = 0; y < 200; y += 20) {
      const shouldFill = (hash + x + y) % 3 === 0;
      if (shouldFill) {
        const color = ((x + y) / 20) % 2 === 0 ? color1 : color2;
        pattern.push(`<rect x="${x}" y="${y}" width="20" height="20" fill="${color}"/>`);
      }
    }
  }

  return `
    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="200" fill="#f8f8f8"/>
      ${pattern.join('')}
    </svg>
  `;
};

/**
 * Genera avatar con gradiente
 */
const generateGradientAvatar = (seed, initials, color1, color2) => {
  const gradientId = `gradient-${simpleHash(seed)}`;

  return `
    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="${gradientId}" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${color1};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${color2};stop-opacity:1" />
        </linearGradient>
      </defs>
      <circle cx="100" cy="100" r="100" fill="url(#${gradientId})"/>
      <text x="100" y="120" font-family="Arial, sans-serif" font-size="60"
            font-weight="bold" text-anchor="middle" fill="white" opacity="0.9">
        ${initials}
      </text>
    </svg>
  `;
};

/**
 * Función hash simple para generar números consistentes
 */
const simpleHash = (str) => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash);
};

/**
 * Extrae las iniciales de un nombre o email
 * @param {string} text - Texto del cual extraer iniciales
 * @returns {string} - Iniciales (máximo 2 caracteres)
 */
const getInitials = (text) => {
  if (!text) return 'U';
  
  const words = text.split(/[\s@.]+/).filter(word => word.length > 0);
  if (words.length === 0) return 'U';
  
  if (words.length === 1) {
    return words[0].substring(0, 2).toUpperCase();
  }
  
  return (words[0][0] + words[1][0]).toUpperCase();
};

/**
 * Convierte un SVG string a Data URL
 * @param {string} svgString - SVG como string
 * @returns {string} - Data URL del SVG
 */
export const svgToDataUrl = (svgString) => {
  const base64 = btoa(unescape(encodeURIComponent(svgString)));
  return `data:image/svg+xml;base64,${base64}`;
};

/**
 * Genera un avatar y lo convierte a Data URL
 * @param {string} seed - Texto base
 * @param {string} style - Estilo del avatar
 * @returns {string} - Data URL del avatar
 */
export const generateAvatarDataUrl = (seed, style = null) => {
  const svg = generateRandomAvatar(seed, style);
  return svgToDataUrl(svg);
};

/**
 * Lista de estilos disponibles
 */
export const getAvailableStyles = () => {
  return avatarStyles.map(style => style.name);
};

/**
 * Genera múltiples avatares para selección
 * @param {string} seed - Texto base
 * @param {number} count - Cantidad de avatares a generar
 * @returns {Array} - Array de Data URLs
 */
export const generateMultipleAvatars = (seed, count = 5) => {
  const avatars = [];
  const styles = getAvailableStyles();

  for (let i = 0; i < count; i++) {
    const style = styles[i % styles.length];
    const modifiedSeed = `${seed}-${i}`;
    avatars.push({
      id: i,
      style: style,
      dataUrl: generateAvatarDataUrl(modifiedSeed, style)
    });
  }

  return avatars;
};
