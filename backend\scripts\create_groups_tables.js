const db = require('../config/db');

async function createGroupsTables() {
  try {
    console.log('=== Creando tablas para grupos y asignaciones ===');

    // 1. Tabla de grados/grupos
    await db.query(`
      CREATE TABLE IF NOT EXISTS grados (
        id SERIAL PRIMARY KEY,
        nombre VARCHAR(100) NOT NULL,
        nivel VARCHAR(50) NOT NULL, -- 'Preescolar', 'Primaria', 'Secundaria'
        seccion VARCHAR(10), -- 'A', 'B', 'C', etc.
        capacidad_maxima INTEGER DEFAULT 30,
        institucion_id INTEGER REFERENCES instituciones(id) ON DELETE CASCADE,
        activo BOOLEAN DEFAULT true,
        fecha_creacion TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(nombre, seccion, institucion_id)
      )
    `);
    console.log('✅ Tabla grados creada');

    // 2. Tabla de asignaciones docente-grado
    await db.query(`
      CREATE TABLE IF NOT EXISTS asignaciones_docente (
        id SERIAL PRIMARY KEY,
        docente_id INTEGER REFERENCES usuarios(id) ON DELETE CASCADE,
        grado_id INTEGER REFERENCES grados(id) ON DELETE CASCADE,
        materia VARCHAR(100), -- Materia que enseña en ese grado
        es_tutor BOOLEAN DEFAULT false, -- Si es el tutor principal del grado
        activo BOOLEAN DEFAULT true,
        fecha_asignacion TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(docente_id, grado_id, materia)
      )
    `);
    console.log('✅ Tabla asignaciones_docente creada');

    // 3. Tabla de grupos de WhatsApp
    await db.query(`
      CREATE TABLE IF NOT EXISTS grupos_whatsapp (
        id SERIAL PRIMARY KEY,
        nombre VARCHAR(255) NOT NULL,
        descripcion TEXT,
        tipo VARCHAR(50) NOT NULL, -- 'grado', 'docentes', 'padres', 'administrativo'
        grado_id INTEGER REFERENCES grados(id) ON DELETE SET NULL,
        chat_id VARCHAR(255), -- ID del grupo en WhatsApp
        activo BOOLEAN DEFAULT true,
        institucion_id INTEGER REFERENCES instituciones(id) ON DELETE CASCADE,
        fecha_creacion TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Tabla grupos_whatsapp creada');

    // 4. Tabla de miembros de grupos de WhatsApp
    await db.query(`
      CREATE TABLE IF NOT EXISTS miembros_grupo_whatsapp (
        id SERIAL PRIMARY KEY,
        grupo_id INTEGER REFERENCES grupos_whatsapp(id) ON DELETE CASCADE,
        telefono VARCHAR(25) NOT NULL,
        nombre VARCHAR(255),
        rol VARCHAR(50) DEFAULT 'miembro', -- 'admin', 'miembro'
        activo BOOLEAN DEFAULT true,
        fecha_ingreso TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(grupo_id, telefono)
      )
    `);
    console.log('✅ Tabla miembros_grupo_whatsapp creada');

    // 5. Actualizar tabla alumnos para incluir grado_id
    await db.query(`
      ALTER TABLE alumnos 
      ADD COLUMN IF NOT EXISTS grado_id INTEGER REFERENCES grados(id) ON DELETE SET NULL
    `);
    console.log('✅ Columna grado_id agregada a alumnos');

    // 6. Actualizar tabla mensajes_whatsapp para incluir grupo_id
    await db.query(`
      ALTER TABLE mensajes_whatsapp 
      ADD COLUMN IF NOT EXISTS grupo_id INTEGER REFERENCES grupos_whatsapp(id) ON DELETE SET NULL,
      ADD COLUMN IF NOT EXISTS es_grupo BOOLEAN DEFAULT false
    `);
    console.log('✅ Columnas de grupo agregadas a mensajes_whatsapp');

    // 7. Insertar grados por defecto
    await insertDefaultGrades();

    console.log('\n✅ Todas las tablas de grupos creadas exitosamente');
    
  } catch (error) {
    console.error('❌ Error creando tablas de grupos:', error);
    throw error;
  }
}

async function insertDefaultGrades() {
  try {
    console.log('\n=== Insertando grados por defecto ===');

    const grados = [
      // Preescolar
      { nombre: 'Kinder 3', nivel: 'Preescolar', seccion: 'A' },
      { nombre: 'Kinder 4', nivel: 'Preescolar', seccion: 'A' },
      { nombre: 'Kinder 5', nivel: 'Preescolar', seccion: 'A' },
      
      // Primaria
      { nombre: '1er Grado', nivel: 'Primaria', seccion: 'A' },
      { nombre: '2do Grado', nivel: 'Primaria', seccion: 'A' },
      { nombre: '3er Grado', nivel: 'Primaria', seccion: 'A' },
      { nombre: '4to Grado', nivel: 'Primaria', seccion: 'A' },
      { nombre: '5to Grado', nivel: 'Primaria', seccion: 'A' },
      { nombre: '6to Grado', nivel: 'Primaria', seccion: 'A' },
      
      // Secundaria
      { nombre: '7mo Grado', nivel: 'Secundaria', seccion: 'A' },
      { nombre: '8vo Grado', nivel: 'Secundaria', seccion: 'A' },
      { nombre: '9no Grado', nivel: 'Secundaria', seccion: 'A' }
    ];

    for (const grado of grados) {
      await db.query(`
        INSERT INTO grados (nombre, nivel, seccion, institucion_id)
        VALUES ($1, $2, $3, 1)
        ON CONFLICT (nombre, seccion, institucion_id) DO NOTHING
      `, [grado.nombre, grado.nivel, grado.seccion]);
    }

    console.log('✅ Grados por defecto insertados');

    // Insertar grupos de WhatsApp por defecto
    await insertDefaultWhatsAppGroups();

  } catch (error) {
    console.error('❌ Error insertando grados por defecto:', error);
  }
}

async function insertDefaultWhatsAppGroups() {
  try {
    console.log('\n=== Insertando grupos de WhatsApp por defecto ===');

    const grupos = [
      { nombre: 'Docentes CCJAP', descripcion: 'Grupo de todos los docentes', tipo: 'docentes' },
      { nombre: 'Directivos CCJAP', descripcion: 'Grupo de directivos y administración', tipo: 'administrativo' },
      { nombre: 'Padres de Familia - General', descripcion: 'Grupo general de padres de familia', tipo: 'padres' }
    ];

    for (const grupo of grupos) {
      await db.query(`
        INSERT INTO grupos_whatsapp (nombre, descripcion, tipo, institucion_id)
        VALUES ($1, $2, $3, 1)
        ON CONFLICT DO NOTHING
      `, [grupo.nombre, grupo.descripcion, grupo.tipo]);
    }

    // Crear grupos por grado
    const gradosResult = await db.query('SELECT id, nombre FROM grados WHERE institucion_id = 1');
    
    for (const grado of gradosResult.rows) {
      await db.query(`
        INSERT INTO grupos_whatsapp (nombre, descripcion, tipo, grado_id, institucion_id)
        VALUES ($1, $2, $3, $4, 1)
        ON CONFLICT DO NOTHING
      `, [
        `Padres ${grado.nombre}`,
        `Grupo de padres de familia de ${grado.nombre}`,
        'grado',
        grado.id
      ]);
    }

    console.log('✅ Grupos de WhatsApp por defecto insertados');

  } catch (error) {
    console.error('❌ Error insertando grupos de WhatsApp:', error);
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  createGroupsTables()
    .then(() => {
      console.log('\n🎉 Configuración de grupos completada');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = { createGroupsTables };
