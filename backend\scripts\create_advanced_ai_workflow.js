const axios = require('axios');

// Configuración
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0ZjM0ODNlMC0xMjViLTRjMGItYTI4ZS1mNGJkMTJlYTdmNzAiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ4MzI2Mzg0fQ.YHpG_nNe3ssUgOT7JmYm4a5o8VV221oQZHM3F4bIzzM';

// Workflow avanzado con ChatGPT y memoria
async function createAdvancedAIWorkflow() {
  const workflowData = {
    name: 'WhatsApp AI Agent with Memory & ChatGPT',
    settings: {
      executionOrder: 'v1'
    },
    nodes: [
      // 1. Webhook para recibir mensajes de WhatsApp
      {
        parameters: {
          httpMethod: 'POST',
          path: 'whatsapp-ai',
          responseMode: 'responseNode',
          options: {}
        },
        id: 'webhook-whatsapp',
        name: 'WhatsApp Webhook',
        type: 'n8n-nodes-base.webhook',
        typeVersion: 1,
        position: [240, 300]
      },

      // 2. Guardar mensaje en base de datos
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              telefono_remitente: '={{ $json.from }}',
              texto_mensaje: '={{ $json.body }}',
              fecha_recepcion: '={{ new Date().toISOString() }}',
              institucion_id: 1,
              procesado: false,
              nombre_remitente: '={{ $json.name || $json.from }}'
            }
          },
          table: 'mensajes_whatsapp',
          dataMode: 'defineBelow'
        },
        id: 'save-message',
        name: 'Guardar Mensaje',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [460, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },

      // 3. Recuperar memoria/historial del usuario
      {
        parameters: {
          operation: 'select',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              telefono_remitente: '={{ $("WhatsApp Webhook").first().json.from }}'
            }
          },
          table: 'mensajes_whatsapp',
          limit: 10,
          sort: {
            values: [
              {
                column: 'fecha_recepcion',
                direction: 'DESC'
              }
            ]
          }
        },
        id: 'get-user-history',
        name: 'Obtener Historial',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [680, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },

      // 4. Preparar contexto para ChatGPT
      {
        parameters: {
          jsCode: `
// Preparar contexto para ChatGPT con memoria
const currentMessage = $input.first().json.body;
const senderPhone = $input.first().json.from;
const senderName = $input.first().json.name || senderPhone;

// Obtener historial de mensajes
const history = $('get-user-history').all().map(item => ({
  message: item.json.texto_mensaje,
  date: item.json.fecha_recepcion,
  processed: item.json.procesado
}));

// Crear contexto de memoria
const memoryContext = history.length > 0 
  ? "Historial de conversación:\\n" + history.slice(0, 5).map((h, i) => 
      \`\${i + 1}. [\${new Date(h.date).toLocaleDateString()}] \${h.message}\`
    ).join("\\n")
  : "Primera conversación con este usuario.";

// Sistema prompt para el agente IA
const systemPrompt = \`Eres un asistente inteligente del Colegio Cristiano Jerusalén de los Altos de Palencia (CCJAP).

INFORMACIÓN DEL COLEGIO:
- Horario de clases: 7:00 AM a 12:00 PM (Lunes a Viernes)
- Horario de oficina: 7:00 AM a 3:00 PM
- Director: Disponible de 8:00 AM a 2:00 PM
- Teléfono principal: +503 1234-5678
- Dirección: Palencia, El Salvador

CAPACIDADES:
1. AUSENCIAS: Puedes procesar reportes de ausencia estudiantil
2. INFORMACIÓN: Responder preguntas sobre horarios, actividades, etc.
3. QUEJAS/PROBLEMAS: Escalar al director cuando sea necesario
4. MEMORIA: Recordar conversaciones anteriores con padres

INSTRUCCIONES:
- Sé amable, profesional y empático
- Si es una ausencia, solicita: nombre completo del estudiante y grado
- Si es una queja seria, informa que notificarás al director
- Usa la memoria de conversaciones para dar respuestas personalizadas
- Responde en español de forma clara y concisa

MEMORIA DE CONVERSACIÓN:
\${memoryContext}

MENSAJE ACTUAL: "\${currentMessage}"
REMITENTE: \${senderName} (\${senderPhone})

Analiza el mensaje y responde apropiadamente. Si es una ausencia, clasifica como "absence". Si requiere atención del director, clasifica como "director_attention".\`;

return [{
  json: {
    system_prompt: systemPrompt,
    user_message: currentMessage,
    sender_phone: senderPhone,
    sender_name: senderName,
    conversation_history: history,
    message_id: $('save-message').first().json.id
  }
}];
`
        },
        id: 'prepare-context',
        name: 'Preparar Contexto',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [900, 300]
      },

      // 5. Llamada a ChatGPT
      {
        parameters: {
          url: 'https://api.openai.com/v1/chat/completions',
          authentication: 'predefinedCredentialType',
          nodeCredentialType: 'openAiApi',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Content-Type',
                value: 'application/json'
              }
            ]
          },
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'model',
                value: 'gpt-4'
              },
              {
                name: 'messages',
                value: '=[{"role": "system", "content": "{{ $json.system_prompt }}"}, {"role": "user", "content": "{{ $json.user_message }}"}]'
              },
              {
                name: 'max_tokens',
                value: 500
              },
              {
                name: 'temperature',
                value: 0.7
              },
              {
                name: 'functions',
                value: '=[{"name": "classify_message", "description": "Classify the message type and extract relevant information", "parameters": {"type": "object", "properties": {"message_type": {"type": "string", "enum": ["absence", "question", "complaint", "director_attention", "general"]}, "confidence": {"type": "number"}, "extracted_data": {"type": "object", "properties": {"student_name": {"type": "string"}, "grade": {"type": "string"}, "reason": {"type": "string"}}}, "requires_human_attention": {"type": "boolean"}, "suggested_response": {"type": "string"}}, "required": ["message_type", "confidence", "suggested_response"]}}]'
              },
              {
                name: 'function_call',
                value: '{"name": "classify_message"}'
              }
            ]
          },
          options: {}
        },
        id: 'chatgpt-analysis',
        name: 'ChatGPT Analysis',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [1120, 300],
        credentials: {
          openAiApi: {
            id: 'openai-api',
            name: 'OpenAI API'
          }
        }
      },

      // 6. Procesar respuesta de ChatGPT
      {
        parameters: {
          jsCode: `
// Procesar respuesta de ChatGPT
const chatgptResponse = $input.first().json;
const originalData = $('prepare-context').first().json;

let analysis = {
  message_type: 'general',
  confidence: 0.5,
  suggested_response: 'Gracias por su mensaje. Un miembro de nuestro equipo se pondrá en contacto con usted.',
  requires_human_attention: false,
  extracted_data: {}
};

try {
  if (chatgptResponse.choices && chatgptResponse.choices[0]) {
    const choice = chatgptResponse.choices[0];
    
    if (choice.function_call && choice.function_call.arguments) {
      const functionArgs = JSON.parse(choice.function_call.arguments);
      analysis = { ...analysis, ...functionArgs };
    } else if (choice.message && choice.message.content) {
      analysis.suggested_response = choice.message.content;
    }
  }
} catch (error) {
  console.error('Error parsing ChatGPT response:', error);
}

return [{
  json: {
    ...analysis,
    original_message: originalData.user_message,
    sender_phone: originalData.sender_phone,
    sender_name: originalData.sender_name,
    message_id: originalData.message_id,
    chatgpt_raw_response: chatgptResponse,
    timestamp: new Date().toISOString()
  }
}];
`
        },
        id: 'process-chatgpt',
        name: 'Procesar ChatGPT',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [1340, 300]
      },

      // 7. Actualizar mensaje con análisis IA
      {
        parameters: {
          operation: 'update',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              ai_analysis: '={{ JSON.stringify($json) }}',
              message_type: '={{ $json.message_type }}',
              requires_attention: '={{ $json.requires_human_attention }}'
            }
          },
          table: 'mensajes_whatsapp',
          updateKey: 'id',
          columnToMatchOn: 'id',
          valueToMatchOn: '={{ $json.message_id }}'
        },
        id: 'update-analysis',
        name: 'Actualizar Análisis',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [1560, 300],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },

      // 8. Decisión: ¿Es ausencia?
      {
        parameters: {
          conditions: {
            options: {
              caseSensitive: true,
              leftValue: '',
              typeValidation: 'strict'
            },
            conditions: [
              {
                id: 'condition1',
                leftValue: '={{ $json.message_type }}',
                rightValue: 'absence',
                operator: {
                  type: 'string',
                  operation: 'equals'
                }
              }
            ],
            combinator: 'and'
          }
        },
        id: 'is-absence',
        name: '¿Es Ausencia?',
        type: 'n8n-nodes-base.if',
        typeVersion: 2,
        position: [1780, 300]
      },

      // 9. Procesar ausencia automáticamente
      {
        parameters: {
          jsCode: `
// Procesar ausencia con IA
const data = $input.first().json;

// Verificar si tenemos información completa
const hasStudentName = data.extracted_data && data.extracted_data.student_name;
const hasGrade = data.extracted_data && data.extracted_data.grade;

let response = {
  action: 'process_absence',
  student_name: hasStudentName ? data.extracted_data.student_name : null,
  grade: hasGrade ? data.extracted_data.grade : null,
  reason: data.extracted_data?.reason || data.original_message,
  needs_more_info: !hasStudentName || !hasGrade,
  phone_number: data.sender_phone,
  sender_name: data.sender_name,
  message_id: data.message_id
};

if (response.needs_more_info) {
  let missingInfo = [];
  if (!hasStudentName) missingInfo.push('nombre completo del estudiante');
  if (!hasGrade) missingInfo.push('grado');
  
  response.reply_message = \`Para procesar la ausencia correctamente, necesito que me proporcione: \${missingInfo.join(' y ')}. ¿Podría ayudarme con esta información?\`;
  response.action = 'request_absence_info';
} else {
  response.reply_message = \`Perfecto. He registrado la ausencia de \${response.student_name} de \${response.grade} grado. Se ha notificado al docente correspondiente y se generará un reporte. ¡Que se mejore pronto!\`;
  response.action = 'create_absence_report';
}

return [{ json: response }];
`
        },
        id: 'process-absence',
        name: 'Procesar Ausencia',
        type: 'n8n-nodes-base.code',
        typeVersion: 2,
        position: [2000, 200]
      },

      // 10. Crear reporte de ausencia en BD
      {
        parameters: {
          operation: 'insert',
          schema: {
            mappingMode: 'defineBelow',
            value: {
              fecha_ausencia: '={{ new Date().toISOString().split("T")[0] }}',
              motivo: 'Ausencia reportada por WhatsApp: {{ $json.reason }}',
              reportado_por_telefono: '={{ $json.phone_number }}',
              reportado_por_nombre: '={{ $json.sender_name }}',
              grado_reportado: '={{ $json.grade }}',
              justificado: true,
              notificado_docente: false
            }
          },
          table: 'ausencias',
          dataMode: 'defineBelow'
        },
        id: 'create-absence-record',
        name: 'Crear Registro Ausencia',
        type: 'n8n-nodes-base.postgres',
        typeVersion: 2.4,
        position: [2220, 200],
        credentials: {
          postgres: {
            id: 'postgres-ccjap',
            name: 'PostgreSQL CCJAP'
          }
        }
      },

      // 11. Decisión: ¿Requiere atención del director?
      {
        parameters: {
          conditions: {
            options: {
              caseSensitive: true,
              leftValue: '',
              typeValidation: 'strict'
            },
            conditions: [
              {
                id: 'condition1',
                leftValue: '={{ $json.requires_human_attention }}',
                rightValue: true,
                operator: {
                  type: 'boolean',
                  operation: 'equal'
                }
              }
            ],
            combinator: 'and'
          }
        },
        id: 'needs-director',
        name: '¿Requiere Director?',
        type: 'n8n-nodes-base.if',
        typeVersion: 2,
        position: [1780, 500]
      },

      // 12. Notificar al Director
      {
        parameters: {
          url: 'http://backend:3001/api/dashboard/notify',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Content-Type',
                value: 'application/json'
              }
            ]
          },
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'type',
                value: 'director_attention_required'
              },
              {
                name: 'message',
                value: '={{ $json }}'
              },
              {
                name: 'priority',
                value: 'high'
              }
            ]
          },
          options: {}
        },
        id: 'notify-director',
        name: 'Notificar Director',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [2000, 600]
      },

      // 13. Enviar respuesta automática
      {
        parameters: {
          url: 'https://api.whatsapp.com/send',
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'phone',
                value: '={{ $json.sender_phone }}'
              },
              {
                name: 'text',
                value: '={{ $json.suggested_response || $json.reply_message }}'
              }
            ]
          },
          options: {}
        },
        id: 'send-auto-response',
        name: 'Respuesta Automática',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [2000, 400]
      },

      // 14. Notificar Dashboard
      {
        parameters: {
          url: 'http://backend:3001/api/dashboard/notify',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Content-Type',
                value: 'application/json'
              }
            ]
          },
          sendBody: true,
          bodyParameters: {
            parameters: [
              {
                name: 'type',
                value: 'new_whatsapp_message'
              },
              {
                name: 'message',
                value: '={{ $json }}'
              }
            ]
          },
          options: {}
        },
        id: 'notify-dashboard',
        name: 'Notificar Dashboard',
        type: 'n8n-nodes-base.httpRequest',
        typeVersion: 4.2,
        position: [2220, 400]
      },

      // 15. Responder al webhook
      {
        parameters: {
          respondWith: 'json',
          responseBody: '{\n  "status": "processed",\n  "message": "Mensaje procesado por IA avanzada",\n  "analysis": {{ JSON.stringify($json) }}\n}'
        },
        id: 'webhook-response',
        name: 'Respuesta Webhook',
        type: 'n8n-nodes-base.respondToWebhook',
        typeVersion: 1,
        position: [2440, 400]
      }
    ],

    connections: {
      'WhatsApp Webhook': {
        main: [
          [
            {
              node: 'Guardar Mensaje',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Guardar Mensaje': {
        main: [
          [
            {
              node: 'Obtener Historial',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Obtener Historial': {
        main: [
          [
            {
              node: 'Preparar Contexto',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Preparar Contexto': {
        main: [
          [
            {
              node: 'ChatGPT Analysis',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'ChatGPT Analysis': {
        main: [
          [
            {
              node: 'Procesar ChatGPT',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Procesar ChatGPT': {
        main: [
          [
            {
              node: 'Actualizar Análisis',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Actualizar Análisis': {
        main: [
          [
            {
              node: '¿Es Ausencia?',
              type: 'main',
              index: 0
            },
            {
              node: '¿Requiere Director?',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      '¿Es Ausencia?': {
        main: [
          [
            {
              node: 'Procesar Ausencia',
              type: 'main',
              index: 0
            }
          ],
          [
            {
              node: 'Respuesta Automática',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Procesar Ausencia': {
        main: [
          [
            {
              node: 'Crear Registro Ausencia',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Crear Registro Ausencia': {
        main: [
          [
            {
              node: 'Respuesta Automática',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      '¿Requiere Director?': {
        main: [
          [
            {
              node: 'Notificar Director',
              type: 'main',
              index: 0
            }
          ],
          []
        ]
      },
      'Notificar Director': {
        main: [
          [
            {
              node: 'Respuesta Automática',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Respuesta Automática': {
        main: [
          [
            {
              node: 'Notificar Dashboard',
              type: 'main',
              index: 0
            }
          ]
        ]
      },
      'Notificar Dashboard': {
        main: [
          [
            {
              node: 'Respuesta Webhook',
              type: 'main',
              index: 0
            }
          ]
        ]
      }
    }
  };

  try {
    const response = await axios.post(`${N8N_BASE_URL}/api/v1/workflows`, workflowData, {
      headers: {
        'Content-Type': 'application/json',
        'X-N8N-API-KEY': N8N_API_KEY
      }
    });

    console.log('✅ Workflow avanzado de IA creado exitosamente');
    console.log(`   ID: ${response.data.id}`);
    console.log(`   Webhook URL: ${N8N_BASE_URL}/webhook/whatsapp-ai`);
    
    return response.data;
  } catch (error) {
    console.error('❌ Error creando workflow avanzado:', error.response?.data || error.message);
    throw error;
  }
}

// Función principal
async function createAdvancedAISystem() {
  try {
    console.log('=== Creando Sistema Avanzado de IA con ChatGPT y Memoria ===');
    
    const workflow = await createAdvancedAIWorkflow();
    
    console.log('\n=== SISTEMA AVANZADO CREADO EXITOSAMENTE ===');
    console.log('✅ Agente IA con ChatGPT configurado');
    console.log('✅ Sistema de memoria implementado');
    console.log('✅ Procesamiento inteligente de ausencias');
    console.log('✅ Notificaciones contextuales al director');
    console.log('✅ Respuestas automáticas personalizadas');
    
    console.log('\n📋 URLs importantes:');
    console.log(`   Webhook IA: ${N8N_BASE_URL}/webhook/whatsapp-ai`);
    console.log(`   Editor n8n: ${N8N_BASE_URL}`);
    
    console.log('\n⚠️  IMPORTANTE: Configurar credenciales de OpenAI en n8n');
    console.log('   1. Ve a n8n → Credentials');
    console.log('   2. Agrega "OpenAI API" credential');
    console.log('   3. Ingresa tu API key de OpenAI');
    
    return workflow;
    
  } catch (error) {
    console.error('Error creando el sistema avanzado:', error);
    throw error;
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  createAdvancedAISystem()
    .then(() => {
      console.log('\n🎉 Sistema avanzado de IA creado exitosamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = { createAdvancedAISystem };
