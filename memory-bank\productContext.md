# Product Context

## High-Level Project Description
- **Project Name:** ccjapDocenteAutomatizacion
- **Description:** A web application for managing educational institutions, including features for user authentication, institution management, and communication via WhatsApp.

## Goals
- Provide a user-friendly interface for managing educational institutions.
- Enable seamless communication with users via WhatsApp.
- Allow customization of institution settings.

## Features
- User authentication and authorization.
- Institution management (add, edit, delete).
- WhatsApp integration for communication.
- Configuration settings for institution name, WhatsApp settings, and other related configurations.

## Overall Architecture
- **Frontend:** React.js
- **Backend:** Node.js with Express
- **Database:** PostgreSQL
- **Communication:** WhatsApp Business API