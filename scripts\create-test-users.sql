-- Script para crear usuarios de prueba con diferentes roles
-- Contraseña para todos: admin123

-- Insertar usuarios de prueba (la contraseña 'admin123' ya está hasheada)
-- Hash generado con bcrypt para 'admin123': $2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi

INSERT INTO usuarios (nombre, email, password_hash, rol, institucion_id) VALUES
-- Director
('<PERSON>', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Director', 1),

-- <PERSON><PERSON><PERSON><PERSON>
('<PERSON>', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Coordinador <PERSON>', 1),

-- <PERSON><PERSON><PERSON>
('<PERSON>', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Docente', 1),
('Luis Hernández', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Docente', 1),
('Carmen López', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Docente', 1),

-- Secretario
('Rosa Pérez', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Secretario', 1)

ON CONFLICT (email) DO NOTHING;

-- Crear algunos grados de ejemplo si no existen
INSERT INTO grados (nombre, institucion_id) VALUES
('1° Básico', 1),
('2° Básico', 1),
('3° Básico', 1),
('4° Básico', 1),
('5° Básico', 1),
('6° Básico', 1),
('7° Básico', 1),
('8° Básico', 1),
('9° Básico', 1),
('1° Bachillerato', 1),
('2° Bachillerato', 1)
ON CONFLICT (nombre, institucion_id) DO NOTHING;

-- Crear algunas asignaciones de ejemplo
-- Obtener IDs de usuarios y materias para las asignaciones
DO $$
DECLARE
    docente1_id INTEGER;
    docente2_id INTEGER;
    docente3_id INTEGER;
    materia_mat_id INTEGER;
    materia_len_id INTEGER;
    materia_cn_id INTEGER;
    materia_ing_id INTEGER;
    grado1_id INTEGER;
    grado2_id INTEGER;
    grado3_id INTEGER;
    horario_lunes_1_id INTEGER;
    horario_lunes_2_id INTEGER;
    horario_martes_1_id INTEGER;
    horario_martes_2_id INTEGER;
BEGIN
    -- Obtener IDs de docentes
    SELECT id INTO docente1_id FROM usuarios WHERE email = '<EMAIL>';
    SELECT id INTO docente2_id FROM usuarios WHERE email = '<EMAIL>';
    SELECT id INTO docente3_id FROM usuarios WHERE email = '<EMAIL>';
    
    -- Obtener IDs de materias
    SELECT id INTO materia_mat_id FROM materias WHERE codigo = 'MAT';
    SELECT id INTO materia_len_id FROM materias WHERE codigo = 'LEN';
    SELECT id INTO materia_cn_id FROM materias WHERE codigo = 'CN';
    SELECT id INTO materia_ing_id FROM materias WHERE codigo = 'ING';
    
    -- Obtener IDs de grados
    SELECT id INTO grado1_id FROM grados WHERE nombre = '1° Básico';
    SELECT id INTO grado2_id FROM grados WHERE nombre = '2° Básico';
    SELECT id INTO grado3_id FROM grados WHERE nombre = '3° Básico';
    
    -- Obtener IDs de horarios
    SELECT id INTO horario_lunes_1_id FROM horarios WHERE dia_semana = 1 AND nombre = '1ra Hora' LIMIT 1;
    SELECT id INTO horario_lunes_2_id FROM horarios WHERE dia_semana = 1 AND nombre = '2da Hora' LIMIT 1;
    SELECT id INTO horario_martes_1_id FROM horarios WHERE dia_semana = 2 AND nombre = '1ra Hora' LIMIT 1;
    SELECT id INTO horario_martes_2_id FROM horarios WHERE dia_semana = 2 AND nombre = '2da Hora' LIMIT 1;
    
    -- Crear asignaciones de ejemplo
    IF docente1_id IS NOT NULL AND materia_mat_id IS NOT NULL AND grado1_id IS NOT NULL AND horario_lunes_1_id IS NOT NULL THEN
        INSERT INTO asignaciones_academicas (docente_id, materia_id, grado_id, horario_id, aula, periodo_academico, fecha_inicio, fecha_fin)
        VALUES (docente1_id, materia_mat_id, grado1_id, horario_lunes_1_id, 'Aula 101', '2025', '2025-01-15', '2025-11-30')
        ON CONFLICT DO NOTHING;
    END IF;
    
    IF docente1_id IS NOT NULL AND materia_len_id IS NOT NULL AND grado1_id IS NOT NULL AND horario_lunes_2_id IS NOT NULL THEN
        INSERT INTO asignaciones_academicas (docente_id, materia_id, grado_id, horario_id, aula, periodo_academico, fecha_inicio, fecha_fin)
        VALUES (docente1_id, materia_len_id, grado1_id, horario_lunes_2_id, 'Aula 101', '2025', '2025-01-15', '2025-11-30')
        ON CONFLICT DO NOTHING;
    END IF;
    
    IF docente2_id IS NOT NULL AND materia_cn_id IS NOT NULL AND grado2_id IS NOT NULL AND horario_martes_1_id IS NOT NULL THEN
        INSERT INTO asignaciones_academicas (docente_id, materia_id, grado_id, horario_id, aula, periodo_academico, fecha_inicio, fecha_fin)
        VALUES (docente2_id, materia_cn_id, grado2_id, horario_martes_1_id, 'Aula 102', '2025', '2025-01-15', '2025-11-30')
        ON CONFLICT DO NOTHING;
    END IF;
    
    IF docente3_id IS NOT NULL AND materia_ing_id IS NOT NULL AND grado3_id IS NOT NULL AND horario_martes_2_id IS NOT NULL THEN
        INSERT INTO asignaciones_academicas (docente_id, materia_id, grado_id, horario_id, aula, periodo_academico, fecha_inicio, fecha_fin)
        VALUES (docente3_id, materia_ing_id, grado3_id, horario_martes_2_id, 'Aula 103', '2025', '2025-01-15', '2025-11-30')
        ON CONFLICT DO NOTHING;
    END IF;
END $$;

-- Crear algunos alumnos de ejemplo
INSERT INTO alumnos (nombre, apellido, fecha_nacimiento, grado_id, telefono_padre, nombre_padre, institucion_id) VALUES
('Juan Carlos', 'Pérez García', '2010-03-15', (SELECT id FROM grados WHERE nombre = '1° Básico' LIMIT 1), '+503 7123-4567', 'Carlos Pérez', 1),
('María Elena', 'González López', '2010-07-22', (SELECT id FROM grados WHERE nombre = '1° Básico' LIMIT 1), '+503 7234-5678', 'Elena López', 1),
('Pedro Antonio', 'Martínez Ruiz', '2009-11-08', (SELECT id FROM grados WHERE nombre = '2° Básico' LIMIT 1), '+503 7345-6789', 'Antonio Martínez', 1),
('Ana Sofía', 'Hernández Castro', '2009-05-12', (SELECT id FROM grados WHERE nombre = '2° Básico' LIMIT 1), '+503 7456-7890', 'Sofía Castro', 1),
('Luis Fernando', 'Rodríguez Morales', '2008-09-30', (SELECT id FROM grados WHERE nombre = '3° Básico' LIMIT 1), '+503 7567-8901', 'Fernando Morales', 1),
('Carmen Rosa', 'López Jiménez', '2008-12-18', (SELECT id FROM grados WHERE nombre = '3° Básico' LIMIT 1), '+503 7678-9012', 'Rosa Jiménez', 1)
ON CONFLICT DO NOTHING;

-- Crear algunas comunicaciones de ejemplo
INSERT INTO comunicaciones_padres (alumno_id, tipo_comunicacion, mensaje_original, telefono_padre, nombre_padre, estado) VALUES
((SELECT id FROM alumnos WHERE nombre = 'Juan Carlos' AND apellido = 'Pérez García' LIMIT 1), 'ausencia', 'Buenos días, mi hijo Juan Carlos no podrá asistir hoy por enfermedad', '+503 7123-4567', 'Carlos Pérez', 'pendiente'),
((SELECT id FROM alumnos WHERE nombre = 'María Elena' AND apellido = 'González López' LIMIT 1), 'solicitud_tareas', 'Hola, ¿podrían enviarme las tareas de María Elena? Estuvo enferma ayer', '+503 7234-5678', 'Elena López', 'pendiente'),
((SELECT id FROM alumnos WHERE nombre = 'Pedro Antonio' AND apellido = 'Martínez Ruiz' LIMIT 1), 'consulta_estado', '¿Cómo va Pedro en matemáticas? Me preocupa que esté teniendo dificultades', '+503 7345-6789', 'Antonio Martínez', 'pendiente')
ON CONFLICT DO NOTHING;

-- Mostrar resumen de lo creado
SELECT 'Usuarios creados:' as tipo, COUNT(*) as cantidad FROM usuarios WHERE email LIKE '%@ccjap.edu.sv'
UNION ALL
SELECT 'Grados creados:', COUNT(*) FROM grados
UNION ALL
SELECT 'Materias creadas:', COUNT(*) FROM materias
UNION ALL
SELECT 'Horarios creados:', COUNT(*) FROM horarios
UNION ALL
SELECT 'Asignaciones creadas:', COUNT(*) FROM asignaciones_academicas
UNION ALL
SELECT 'Alumnos creados:', COUNT(*) FROM alumnos
UNION ALL
SELECT 'Comunicaciones creadas:', COUNT(*) FROM comunicaciones_padres;
