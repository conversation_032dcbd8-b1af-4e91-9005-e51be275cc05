{"version": 3, "sources": ["../src/clerkEventBus.ts", "../src/eventBus.ts"], "sourcesContent": ["import type { ClerkEventPayload } from '@clerk/types';\n\nimport { createEventBus } from './eventBus';\n\nexport const clerkEvents = {\n  Status: 'status',\n} satisfies Record<string, keyof ClerkEventPayload>;\n\nexport const createClerkEventBus = () => {\n  return createEventBus<ClerkEventPayload>();\n};\n", "/**\n * Type definition for event handler functions\n */\ntype EventHandler<Events extends Record<string, unknown>, Key extends keyof Events> = (payload: Events[Key]) => void;\n\n/**\n * @interface\n * Strongly-typed event bus interface that enables type-safe publish/subscribe patterns\n */\ntype EventBus<Events extends Record<string, unknown>> = {\n  /**\n   * Subscribe to an event\n   *\n   * @param event - The event name to subscribe to\n   * @param handler - The callback function to execute when the event is dispatched\n   * @param opts - Optional configuration\n   * @param opts.notify - If true and the event was previously dispatched, handler will be called immediately with the latest payload\n   * @returns void\n   */\n  on: <Event extends keyof Events>(\n    event: Event,\n    handler: EventHandler<Events, Event>,\n    opts?: { notify?: boolean },\n  ) => void;\n\n  /**\n   * Subscribe to an event with pre-dispatch priority\n   * Pre-dispatch handlers are called before regular event handlers when an event is dispatched\n   *\n   * @param event - The event name to subscribe to\n   * @param handler - The callback function to execute when the event is dispatched\n   * @returns void\n   */\n  prioritizedOn: <Event extends keyof Events>(event: Event, handler: EventHandler<Events, Event>) => void;\n\n  /**\n   * Publish an event with a payload\n   * Triggers all registered handlers for the event\n   *\n   * @param event - The event name to publish\n   * @param payload - The data to pass to event handlers\n   * @returns void\n   */\n  emit: <Event extends keyof Events>(event: Event, payload: Events[Event]) => void;\n\n  /**\n   * Unsubscribe from an event\n   *\n   * @param event - The event name to unsubscribe from\n   * @param handler - Optional specific handler to remove. If omitted, all handlers for the event are removed\n   * @returns void\n   */\n  off: <Event extends keyof Events>(event: Event, handler?: EventHandler<Events, Event>) => void;\n\n  /**\n   * Unsubscribe from a pre-dispatch event\n   *\n   * @param event - The event name to unsubscribe from\n   * @param handler - Optional specific handler to remove. If omitted, all pre-dispatch handlers for the event are removed\n   * @returns void\n   */\n  prioritizedOff: <Event extends keyof Events>(event: Event, handler?: EventHandler<Events, Event>) => void;\n\n  /**\n   * Internal utilities for the event bus\n   */\n  internal: {\n    /**\n     * Retrieve all listeners for a specific event\n     *\n     * @param event - The event name to get listeners for\n     * @returns Array of handler functions\n     */\n    retrieveListeners: <Event extends keyof Events>(event: Event) => Array<(...args: any[]) => void>;\n  };\n};\n\n/**\n * @internal\n */\ntype InternalOn = <Events extends Record<string, unknown>, Event extends keyof Events>(\n  eventToHandlersMap: Map<keyof Events, Array<(...args: any[]) => void>>,\n  latestPayloadMap: Map<keyof Events, any>,\n  event: Event,\n  handler: EventHandler<Events, Event>,\n  opts?: { notify?: boolean },\n) => void;\n\n/**\n * @internal\n */\ntype InternalOff = <Events extends Record<string, unknown>, Event extends keyof Events>(\n  eventToHandlersMap: Map<keyof Events, Array<(...args: any[]) => void>>,\n  event: Event,\n  handler?: EventHandler<Events, Event>,\n) => void;\n\n/**\n * @internal\n */\ntype InternalDispatch = <Events extends Record<string, unknown>, Event extends keyof Events>(\n  eventToHandlersMap: Map<keyof Events, Array<(...args: any[]) => void>>,\n  event: Event,\n  payload: Events[Event],\n) => void;\n\n/**\n * @internal\n */\nconst _on: InternalOn = (eventToHandlersMap, latestPayloadMap, event, handler, opts) => {\n  const { notify } = opts || {};\n  let handlers = eventToHandlersMap.get(event);\n\n  if (!handlers) {\n    handlers = [];\n    eventToHandlersMap.set(event, handlers);\n  }\n\n  handlers.push(handler);\n\n  if (notify && latestPayloadMap.has(event)) {\n    handler(latestPayloadMap.get(event));\n  }\n};\n\n/**\n * @internal\n */\nconst _dispatch: InternalDispatch = (eventToHandlersMap, event, payload) =>\n  (eventToHandlersMap.get(event) || []).map(h => h(payload));\n\n/**\n * @internal\n */\nconst _off: InternalOff = (eventToHandlersMap, event, handler) => {\n  const handlers = eventToHandlersMap.get(event);\n  if (handlers) {\n    if (handler) {\n      handlers.splice(handlers.indexOf(handler) >>> 0, 1);\n    } else {\n      eventToHandlersMap.set(event, []);\n    }\n  }\n};\n\n/**\n * A ES6/2015 compatible 300 byte event bus\n *\n * Creates a strongly-typed event bus that enables publish/subscribe communication between components.\n *\n * @template Events - A record type that maps event names to their payload types\n * @returns An EventBus instance with the following methods:\n * - `on`: Subscribe to an event\n * - `onPreDispatch`: Subscribe to an event, triggered before regular subscribers\n * - `emit`: Publish an event with payload\n * - `off`: Unsubscribe from an event\n * - `offPreDispatch`: Unsubscribe from a pre-dispatch event\n *\n * @example\n * // Define event types\n * const eventBus = createEventBus<{\n *   'user-login': { userId: string; timestamp: number };\n *   'data-updated': { records: any[] };\n *   'error': Error;\n * }>();\n *\n * // Subscribe to events\n * eventBus.on('user-login', ({ userId, timestamp }) => {\n *   console.log(`User ${userId} logged in at ${timestamp}`);\n * });\n *\n * // Subscribe with immediate notification if event was already dispatched\n * eventBus.on('user-login', (payload) => {\n *   // This will be called immediately if 'user-login' was previously dispatched\n * }, { notify: true });\n *\n * // Publish an event\n * eventBus.emit('user-login', { userId: 'abc123', timestamp: Date.now() });\n *\n * // Unsubscribe from event\n * const handler = (payload) => console.log(payload);\n * eventBus.on('error', handler);\n * // Later...\n * eventBus.off('error', handler);\n *\n * // Unsubscribe all handlers for an event\n * eventBus.off('data-updated');\n */\nexport const createEventBus = <Events extends Record<string, unknown>>(): EventBus<Events> => {\n  const eventToHandlersMap = new Map<keyof Events, Array<(...args: any[]) => void>>();\n  const latestPayloadMap = new Map<keyof Events, any>();\n  const eventToPredispatchHandlersMap = new Map<keyof Events, Array<(...args: any[]) => void>>();\n\n  const emit: EventBus<Events>['emit'] = (event, payload) => {\n    latestPayloadMap.set(event, payload);\n    _dispatch(eventToPredispatchHandlersMap, event, payload);\n    _dispatch(eventToHandlersMap, event, payload);\n  };\n\n  return {\n    // Subscribe to an event\n    on: (...args) => _on(eventToHandlersMap, latestPayloadMap, ...args),\n    // Subscribe to an event with priority\n    // Registered handlers with `prioritizedOn` will be called before handlers registered with `on`\n    prioritizedOn: (...args) => _on(eventToPredispatchHandlersMap, latestPayloadMap, ...args),\n    // Dispatch an event\n    emit,\n    // Unsubscribe from an event\n    off: (...args) => _off(eventToHandlersMap, ...args),\n    // Unsubscribe from an event with priority\n    // Unsubscribes handlers only registered with `prioritizedOn`\n    prioritizedOff: (...args) => _off(eventToPredispatchHandlersMap, ...args),\n\n    // Internal utilities\n    internal: {\n      retrieveListeners: event => eventToHandlersMap.get(event) || [],\n    },\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AC6GA,IAAM,MAAkB,CAAC,oBAAoB,kBAAkB,OAAO,SAAS,SAAS;AACtF,QAAM,EAAE,OAAO,IAAI,QAAQ,CAAC;AAC5B,MAAI,WAAW,mBAAmB,IAAI,KAAK;AAE3C,MAAI,CAAC,UAAU;AACb,eAAW,CAAC;AACZ,uBAAmB,IAAI,OAAO,QAAQ;AAAA,EACxC;AAEA,WAAS,KAAK,OAAO;AAErB,MAAI,UAAU,iBAAiB,IAAI,KAAK,GAAG;AACzC,YAAQ,iBAAiB,IAAI,KAAK,CAAC;AAAA,EACrC;AACF;AAKA,IAAM,YAA8B,CAAC,oBAAoB,OAAO,aAC7D,mBAAmB,IAAI,KAAK,KAAK,CAAC,GAAG,IAAI,OAAK,EAAE,OAAO,CAAC;AAK3D,IAAM,OAAoB,CAAC,oBAAoB,OAAO,YAAY;AAChE,QAAM,WAAW,mBAAmB,IAAI,KAAK;AAC7C,MAAI,UAAU;AACZ,QAAI,SAAS;AACX,eAAS,OAAO,SAAS,QAAQ,OAAO,MAAM,GAAG,CAAC;AAAA,IACpD,OAAO;AACL,yBAAmB,IAAI,OAAO,CAAC,CAAC;AAAA,IAClC;AAAA,EACF;AACF;AA6CO,IAAM,iBAAiB,MAAgE;AAC5F,QAAM,qBAAqB,oBAAI,IAAmD;AAClF,QAAM,mBAAmB,oBAAI,IAAuB;AACpD,QAAM,gCAAgC,oBAAI,IAAmD;AAE7F,QAAM,OAAiC,CAAC,OAAO,YAAY;AACzD,qBAAiB,IAAI,OAAO,OAAO;AACnC,cAAU,+BAA+B,OAAO,OAAO;AACvD,cAAU,oBAAoB,OAAO,OAAO;AAAA,EAC9C;AAEA,SAAO;AAAA;AAAA,IAEL,IAAI,IAAI,SAAS,IAAI,oBAAoB,kBAAkB,GAAG,IAAI;AAAA;AAAA;AAAA,IAGlE,eAAe,IAAI,SAAS,IAAI,+BAA+B,kBAAkB,GAAG,IAAI;AAAA;AAAA,IAExF;AAAA;AAAA,IAEA,KAAK,IAAI,SAAS,KAAK,oBAAoB,GAAG,IAAI;AAAA;AAAA;AAAA,IAGlD,gBAAgB,IAAI,SAAS,KAAK,+BAA+B,GAAG,IAAI;AAAA;AAAA,IAGxE,UAAU;AAAA,MACR,mBAAmB,WAAS,mBAAmB,IAAI,KAAK,KAAK,CAAC;AAAA,IAChE;AAAA,EACF;AACF;;;ADtNO,IAAM,cAAc;AAAA,EACzB,QAAQ;AACV;AAEO,IAAM,sBAAsB,MAAM;AACvC,SAAO,eAAkC;AAC3C;", "names": []}