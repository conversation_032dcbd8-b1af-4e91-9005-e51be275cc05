import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

function LoginForm() {
  const [email, setEmail] = useState('<EMAIL>'); // Credenciales por defecto
  const [password, setPassword] = useState('admin123'); // Credenciales por defecto
  const { login, isLoading, error: authError } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();

    console.log('🔐 Iniciando login con:', { email, password: '***' });
    const result = await login(email, password);

    if (result.success) {
      console.log('✅ Login exitoso, redirigiendo...');
      navigate('/');
    } else {
      console.log('❌ Login fallido:', result.error);
    }
  };

  const clearStorage = () => {
    try {
      localStorage.clear();
      sessionStorage.clear();
      alert('✅ Storage limpiado exitosamente. Ahora puedes intentar iniciar sesión nuevamente.');
      console.log('🧹 Storage limpiado por el usuario');
      window.location.reload(); // Recargar para reinicializar el estado
    } catch (error) {
      console.error('Error al limpiar storage:', error);
      alert('❌ Error al limpiar storage: ' + error.message);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-white dark:bg-dark-bg text-slate-800 dark:text-dark-text">
      <div className="p-8 bg-white dark:bg-dark-card-bg shadow-xl rounded-lg max-w-md w-full border border-transparent dark:border-dark-border">
        <h2 className="text-2xl font-bold text-center text-slate-700 dark:text-dark-text mb-6">Iniciar Sesión</h2>
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="email" className="block text-sm font-medium text-slate-700 dark:text-gray-300 mb-1">Email:</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border border-slate-300 dark:border-dark-border rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-slate-700 text-slate-800 dark:text-dark-text"
              autoComplete="email"
            />
          </div>
          <div className="mb-6">
            <label htmlFor="password" className="block text-sm font-medium text-slate-700 dark:text-gray-300 mb-1">Contraseña:</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-3 py-2 border border-slate-300 dark:border-dark-border rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-slate-700 text-slate-800 dark:text-dark-text"
              autoComplete="current-password"
            />
          </div>
          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
          >
            {isLoading ? '🔄 Iniciando sesión...' : '🔐 Ingresar'}
          </button>
        </form>

        {authError && (
          <div className="mt-4">
            <p className="text-sm text-red-600 bg-red-100 p-3 rounded-md">{authError}</p>
            <button
              onClick={clearStorage}
              className="mt-2 w-full bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-md text-sm"
            >
              🧹 Limpiar Storage y Reintentar
            </button>
          </div>
        )}

        <div className="mt-4 text-center">
          <button
            onClick={clearStorage}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            ¿Problemas con el token? Limpiar storage
          </button>
        </div>
      </div>
    </div>
  );
}

export default LoginForm;
