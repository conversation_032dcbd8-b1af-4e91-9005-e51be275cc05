<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificación Completa del Sistema - CCJAP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .check-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .status-ok { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Verificación Completa del Sistema CCJAP</h1>
        <p>Herramienta de diagnóstico para verificar el estado completo del sistema.</p>
        
        <div class="check-section">
            <h3>🚀 Verificación Rápida</h3>
            <button class="btn btn-success" onclick="runCompleteCheck()">🔍 Ejecutar Verificación Completa</button>
            <div id="quickCheck" class="result" style="display: none;"></div>
        </div>
        
        <div class="check-section">
            <h3>👤 Estado del Usuario</h3>
            <button class="btn" onclick="checkUser()">Verificar Usuario</button>
            <button class="btn" onclick="checkToken()">Verificar Token</button>
            <div id="userStatus" class="result" style="display: none;"></div>
        </div>
        
        <div class="check-section">
            <h3>🔐 Permisos y Acceso</h3>
            <button class="btn" onclick="checkPermissions()">Verificar Permisos</button>
            <button class="btn" onclick="testNavigation()">Probar Navegación</button>
            <div id="permissionsStatus" class="result" style="display: none;"></div>
        </div>
        
        <div class="check-section">
            <h3>🌐 Conectividad del Sistema</h3>
            <button class="btn" onclick="checkBackend()">Verificar Backend</button>
            <button class="btn" onclick="checkDatabase()">Probar Base de Datos</button>
            <div id="connectivityStatus" class="result" style="display: none;"></div>
        </div>
        
        <div class="check-section">
            <h3>🛠️ Acciones de Solución</h3>
            <button class="btn btn-danger" onclick="clearStorage()">🗑️ Limpiar Storage</button>
            <button class="btn btn-success" onclick="createSuperadmin()">👑 Crear Superadmin</button>
            <button class="btn" onclick="goToLogin()">🔑 Ir al Login</button>
            <div id="actionsResult" class="result" style="display: none;"></div>
        </div>
        
        <div id="systemStatus" class="status-grid" style="display: none;"></div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.innerHTML = message;
            element.style.display = 'block';
        }

        async function runCompleteCheck() {
            showResult('quickCheck', '🔄 Ejecutando verificación completa...', 'info');
            
            const checks = [];
            
            // 1. Verificar token
            const token = localStorage.getItem('token');
            checks.push({
                name: 'Token JWT',
                status: token ? 'ok' : 'error',
                message: token ? 'Token presente' : 'Token no encontrado'
            });
            
            // 2. Verificar usuario
            try {
                const userResponse = await fetch('http://localhost:3001/api/users/me', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                const userData = await userResponse.json();
                
                checks.push({
                    name: 'Usuario Autenticado',
                    status: userResponse.ok ? 'ok' : 'error',
                    message: userResponse.ok ? `${userData.nombre} (${userData.rol})` : 'Error de autenticación'
                });
                
                if (userResponse.ok) {
                    checks.push({
                        name: 'Rol Superadministrador',
                        status: userData.rol === 'Superadministrador' ? 'ok' : 'warning',
                        message: userData.rol === 'Superadministrador' ? 'Confirmado' : `Rol actual: ${userData.rol}`
                    });
                }
            } catch (error) {
                checks.push({
                    name: 'Conectividad Backend',
                    status: 'error',
                    message: 'Error de conexión'
                });
            }
            
            // 3. Verificar navegación
            const currentPath = window.location.pathname;
            checks.push({
                name: 'Navegación',
                status: 'ok',
                message: `Página actual: ${currentPath}`
            });
            
            // Mostrar resultados
            let statusHtml = '<div class="status-grid">';
            let detailsHtml = '📊 VERIFICACIÓN COMPLETA DEL SISTEMA\n\n';
            
            checks.forEach(check => {
                const statusClass = check.status === 'ok' ? 'status-ok' : 
                                  check.status === 'warning' ? 'status-warning' : 'status-error';
                const icon = check.status === 'ok' ? '✅' : 
                           check.status === 'warning' ? '⚠️' : '❌';
                
                statusHtml += `
                    <div class="status-card ${statusClass}">
                        <div style="font-size: 24px; margin-bottom: 10px;">${icon}</div>
                        <h4>${check.name}</h4>
                        <p>${check.message}</p>
                    </div>
                `;
                
                detailsHtml += `${icon} ${check.name}: ${check.message}\n`;
            });
            
            statusHtml += '</div>';
            document.getElementById('systemStatus').innerHTML = statusHtml;
            document.getElementById('systemStatus').style.display = 'block';
            
            detailsHtml += '\n🎯 RECOMENDACIONES:\n';
            if (checks.some(c => c.status === 'error')) {
                detailsHtml += '• Hay errores críticos que requieren atención\n';
                detailsHtml += '• Usar las acciones de solución disponibles\n';
            } else if (checks.some(c => c.status === 'warning')) {
                detailsHtml += '• Sistema funcional con advertencias menores\n';
            } else {
                detailsHtml += '• ✅ Sistema funcionando correctamente\n';
            }
            
            showResult('quickCheck', detailsHtml, 
                checks.some(c => c.status === 'error') ? 'error' : 
                checks.some(c => c.status === 'warning') ? 'warning' : 'success'
            );
        }

        async function checkUser() {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('http://localhost:3001/api/users/me', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                const data = await response.json();
                
                if (response.ok) {
                    const message = `✅ USUARIO AUTENTICADO:
ID: ${data.id}
Nombre: ${data.nombre}
Email: ${data.email}
Rol: ${data.rol}
Institución: ${data.institucion_id || 'N/A'}

🔍 ANÁLISIS:
¿Es Superadministrador? ${data.rol === 'Superadministrador' ? 'SÍ ✅' : 'NO ❌'}
¿Email correcto? ${data.email === '<EMAIL>' ? 'SÍ ✅' : 'NO ❌'}`;
                    
                    showResult('userStatus', message, 'success');
                } else {
                    showResult('userStatus', `❌ Error: ${data.error}`, 'error');
                }
            } catch (error) {
                showResult('userStatus', `❌ Error de conexión: ${error.message}`, 'error');
            }
        }

        async function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            showResult('actionsResult', '✅ Storage limpiado exitosamente', 'success');
        }

        async function createSuperadmin() {
            try {
                const response = await fetch('http://localhost:3001/api/auth/create-superadmin', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();
                
                if (data.success) {
                    showResult('actionsResult', `✅ ${data.message}`, 'success');
                } else {
                    showResult('actionsResult', `❌ Error: ${data.error}`, 'error');
                }
            } catch (error) {
                showResult('actionsResult', `❌ Error: ${error.message}`, 'error');
            }
        }

        function goToLogin() {
            window.location.href = '/';
        }

        // Ejecutar verificación automática al cargar
        window.onload = function() {
            setTimeout(runCompleteCheck, 1000);
        };
    </script>
</body>
</html>
