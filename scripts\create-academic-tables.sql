-- Script para crear tablas del sistema académico completo
-- Ejecutar después de la inicialización básica

-- Tabla de materias/asignaturas
CREATE TABLE IF NOT EXISTS materias (
    id SERIAL PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    codigo VARCHAR(20) UNIQUE NOT NULL,
    descripcion TEXT,
    horas_semanales INTEGER DEFAULT 1,
    institucion_id INTEGER REFERENCES instituciones(id),
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de horarios (bloques de tiempo)
CREATE TABLE IF NOT EXISTS horarios (
    id SERIAL PRIMARY KEY,
    nombre VARCHAR(50) NOT NULL, -- Ej: "1ra hora", "2da hora", etc.
    hora_inicio TIME NOT NULL,
    hora_fin TIME NOT NULL,
    dia_semana INTEGER NOT NULL CHECK (dia_semana BETWEEN 1 AND 7), -- 1=Lunes, 7=Domingo
    institucion_id INTEGER REFERENCES instituciones(id),
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de asignaciones docente-materia-grado-horario
CREATE TABLE IF NOT EXISTS asignaciones_academicas (
    id SERIAL PRIMARY KEY,
    docente_id INTEGER REFERENCES usuarios(id),
    materia_id INTEGER REFERENCES materias(id),
    grado_id INTEGER REFERENCES grados(id),
    horario_id INTEGER REFERENCES horarios(id),
    aula VARCHAR(50),
    periodo_academico VARCHAR(20) DEFAULT '2025',
    fecha_inicio DATE,
    fecha_fin DATE,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(docente_id, horario_id, fecha_inicio, fecha_fin) -- Un docente no puede estar en dos lugares al mismo tiempo
);

-- Tabla de comunicaciones con padres
CREATE TABLE IF NOT EXISTS comunicaciones_padres (
    id SERIAL PRIMARY KEY,
    alumno_id INTEGER REFERENCES alumnos(id),
    tipo_comunicacion VARCHAR(50) NOT NULL, -- 'ausencia', 'solicitud_tareas', 'consulta_estado', 'otro'
    mensaje_original TEXT NOT NULL,
    mensaje_procesado TEXT, -- Mensaje procesado por IA
    respuesta_automatica TEXT,
    respuesta_manual TEXT,
    estado VARCHAR(30) DEFAULT 'pendiente', -- 'pendiente', 'procesado', 'respondido', 'cerrado'
    prioridad VARCHAR(20) DEFAULT 'normal', -- 'baja', 'normal', 'alta', 'urgente'
    telefono_padre VARCHAR(20),
    nombre_padre VARCHAR(100),
    procesado_por_ia BOOLEAN DEFAULT false,
    procesado_por INTEGER REFERENCES usuarios(id), -- Usuario que procesó manualmente
    fecha_procesamiento TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de reportes de ausencias mejorada
CREATE TABLE IF NOT EXISTS reportes_ausencias (
    id SERIAL PRIMARY KEY,
    alumno_id INTEGER REFERENCES alumnos(id),
    fecha_ausencia DATE NOT NULL,
    tipo_ausencia VARCHAR(30) DEFAULT 'injustificada', -- 'justificada', 'injustificada', 'tardanza', 'salida_temprana'
    motivo TEXT,
    justificacion TEXT,
    documento_adjunto VARCHAR(255), -- Ruta del archivo de justificación
    reportado_por INTEGER REFERENCES usuarios(id),
    comunicacion_id INTEGER REFERENCES comunicaciones_padres(id), -- Si viene de WhatsApp
    notificado_padre BOOLEAN DEFAULT false,
    fecha_notificacion TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de tareas y actividades
CREATE TABLE IF NOT EXISTS tareas_actividades (
    id SERIAL PRIMARY KEY,
    asignacion_id INTEGER REFERENCES asignaciones_academicas(id),
    titulo VARCHAR(200) NOT NULL,
    descripcion TEXT,
    fecha_asignacion DATE DEFAULT CURRENT_DATE,
    fecha_entrega DATE,
    tipo_actividad VARCHAR(50) DEFAULT 'tarea', -- 'tarea', 'examen', 'proyecto', 'actividad'
    valor_puntos DECIMAL(5,2) DEFAULT 0,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de calificaciones
CREATE TABLE IF NOT EXISTS calificaciones (
    id SERIAL PRIMARY KEY,
    alumno_id INTEGER REFERENCES alumnos(id),
    tarea_id INTEGER REFERENCES tareas_actividades(id),
    calificacion DECIMAL(5,2),
    comentarios TEXT,
    fecha_calificacion DATE DEFAULT CURRENT_DATE,
    calificado_por INTEGER REFERENCES usuarios(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(alumno_id, tarea_id)
);

-- Tabla de notificaciones del sistema
CREATE TABLE IF NOT EXISTS notificaciones_sistema (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER REFERENCES usuarios(id),
    tipo VARCHAR(50) NOT NULL, -- 'ausencia', 'tarea', 'comunicacion', 'sistema'
    titulo VARCHAR(200) NOT NULL,
    mensaje TEXT NOT NULL,
    leida BOOLEAN DEFAULT false,
    url_accion VARCHAR(255), -- URL para redirigir cuando se hace clic
    datos_adicionales JSONB, -- Datos extra en formato JSON
    fecha_expiracion TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Índices para mejorar rendimiento
CREATE INDEX IF NOT EXISTS idx_asignaciones_docente ON asignaciones_academicas(docente_id);
CREATE INDEX IF NOT EXISTS idx_asignaciones_grado ON asignaciones_academicas(grado_id);
CREATE INDEX IF NOT EXISTS idx_asignaciones_horario ON asignaciones_academicas(horario_id);
CREATE INDEX IF NOT EXISTS idx_comunicaciones_alumno ON comunicaciones_padres(alumno_id);
CREATE INDEX IF NOT EXISTS idx_comunicaciones_estado ON comunicaciones_padres(estado);
CREATE INDEX IF NOT EXISTS idx_reportes_alumno ON reportes_ausencias(alumno_id);
CREATE INDEX IF NOT EXISTS idx_reportes_fecha ON reportes_ausencias(fecha_ausencia);
CREATE INDEX IF NOT EXISTS idx_notificaciones_usuario ON notificaciones_sistema(usuario_id);
CREATE INDEX IF NOT EXISTS idx_notificaciones_leida ON notificaciones_sistema(leida);

-- Insertar datos de ejemplo para materias
INSERT INTO materias (nombre, codigo, descripcion, horas_semanales, institucion_id) VALUES
('Matemáticas', 'MAT', 'Matemáticas básicas y avanzadas', 5, 1),
('Lenguaje', 'LEN', 'Lenguaje y Literatura', 4, 1),
('Ciencias Naturales', 'CN', 'Biología, Química y Física', 3, 1),
('Estudios Sociales', 'ES', 'Historia y Geografía', 3, 1),
('Inglés', 'ING', 'Idioma Inglés', 3, 1),
('Educación Física', 'EF', 'Deportes y Actividad Física', 2, 1),
('Educación Artística', 'EA', 'Arte y Creatividad', 2, 1),
('Informática', 'INF', 'Computación y Tecnología', 2, 1)
ON CONFLICT (codigo) DO NOTHING;

-- Insertar horarios de ejemplo (Lunes a Viernes, 8 horas académicas)
INSERT INTO horarios (nombre, hora_inicio, hora_fin, dia_semana, institucion_id) VALUES
-- Lunes
('1ra Hora', '07:00', '07:45', 1, 1),
('2da Hora', '07:45', '08:30', 1, 1),
('3ra Hora', '08:30', '09:15', 1, 1),
('Recreo', '09:15', '09:30', 1, 1),
('4ta Hora', '09:30', '10:15', 1, 1),
('5ta Hora', '10:15', '11:00', 1, 1),
('6ta Hora', '11:00', '11:45', 1, 1),
('7ma Hora', '11:45', '12:30', 1, 1),
('8va Hora', '12:30', '13:15', 1, 1),
-- Martes
('1ra Hora', '07:00', '07:45', 2, 1),
('2da Hora', '07:45', '08:30', 2, 1),
('3ra Hora', '08:30', '09:15', 2, 1),
('Recreo', '09:15', '09:30', 2, 1),
('4ta Hora', '09:30', '10:15', 2, 1),
('5ta Hora', '10:15', '11:00', 2, 1),
('6ta Hora', '11:00', '11:45', 2, 1),
('7ma Hora', '11:45', '12:30', 2, 1),
('8va Hora', '12:30', '13:15', 2, 1),
-- Miércoles
('1ra Hora', '07:00', '07:45', 3, 1),
('2da Hora', '07:45', '08:30', 3, 1),
('3ra Hora', '08:30', '09:15', 3, 1),
('Recreo', '09:15', '09:30', 3, 1),
('4ta Hora', '09:30', '10:15', 3, 1),
('5ta Hora', '10:15', '11:00', 3, 1),
('6ta Hora', '11:00', '11:45', 3, 1),
('7ma Hora', '11:45', '12:30', 3, 1),
('8va Hora', '12:30', '13:15', 3, 1),
-- Jueves
('1ra Hora', '07:00', '07:45', 4, 1),
('2da Hora', '07:45', '08:30', 4, 1),
('3ra Hora', '08:30', '09:15', 4, 1),
('Recreo', '09:15', '09:30', 4, 1),
('4ta Hora', '09:30', '10:15', 4, 1),
('5ta Hora', '10:15', '11:00', 4, 1),
('6ta Hora', '11:00', '11:45', 4, 1),
('7ma Hora', '11:45', '12:30', 4, 1),
('8va Hora', '12:30', '13:15', 4, 1),
-- Viernes
('1ra Hora', '07:00', '07:45', 5, 1),
('2da Hora', '07:45', '08:30', 5, 1),
('3ra Hora', '08:30', '09:15', 5, 1),
('Recreo', '09:15', '09:30', 5, 1),
('4ta Hora', '09:30', '10:15', 5, 1),
('5ta Hora', '10:15', '11:00', 5, 1),
('6ta Hora', '11:00', '11:45', 5, 1),
('7ma Hora', '11:45', '12:30', 5, 1),
('8va Hora', '12:30', '13:15', 5, 1);

-- Actualizar trigger para updated_at en las nuevas tablas
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_materias_updated_at BEFORE UPDATE ON materias FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_asignaciones_updated_at BEFORE UPDATE ON asignaciones_academicas FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_comunicaciones_updated_at BEFORE UPDATE ON comunicaciones_padres FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reportes_updated_at BEFORE UPDATE ON reportes_ausencias FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tareas_updated_at BEFORE UPDATE ON tareas_actividades FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_calificaciones_updated_at BEFORE UPDATE ON calificaciones FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
