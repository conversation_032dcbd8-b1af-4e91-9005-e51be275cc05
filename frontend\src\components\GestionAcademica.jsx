import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { Calendar, Clock, Users, BookOpen, Plus, Edit, Trash2, Save, X } from 'lucide-react';
import { showNotification } from '../utils/notifications';
import { fadeInUp, slideInLeft, staggerIn } from '../utils/animations';

const GestionAcademica = () => {
  const { currentUser: user, token } = useAuth();
  const [activeTab, setActiveTab] = useState('asignaciones');
  const [loading, setLoading] = useState(false);
  
  // Estados para datos
  const [asignaciones, setAsignaciones] = useState([]);
  const [materias, setMaterias] = useState([]);
  const [horarios, setHorarios] = useState([]);
  const [docentes, setDocentes] = useState([]);
  const [grados, setGrados] = useState([]);
  
  // Estados para formularios
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [formData, setFormData] = useState({});

  useEffect(() => {
    loadInitialData();
    // Aplicar animaciones
    fadeInUp('.academic-container');
    staggerIn('.academic-card', 0.1);
  }, []);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadAsignaciones(),
        loadMaterias(),
        loadHorarios(),
        loadDocentes(),
        loadGrados()
      ]);
    } catch (error) {
      console.error('Error loading initial data:', error);
      showNotification('Error al cargar datos iniciales', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadAsignaciones = async () => {
    try {
      const response = await fetch('/api/academic/asignaciones', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (response.ok) {
        const data = await response.json();
        setAsignaciones(data);
      }
    } catch (error) {
      console.error('Error loading asignaciones:', error);
    }
  };

  const loadMaterias = async () => {
    try {
      const response = await fetch('/api/academic/materias', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (response.ok) {
        const data = await response.json();
        setMaterias(data);
      }
    } catch (error) {
      console.error('Error loading materias:', error);
    }
  };

  const loadHorarios = async () => {
    try {
      const response = await fetch('/api/academic/horarios', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (response.ok) {
        const data = await response.json();
        setHorarios(data);
      }
    } catch (error) {
      console.error('Error loading horarios:', error);
    }
  };

  const loadDocentes = async () => {
    try {
      const response = await fetch('/api/users?rol=Docente', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (response.ok) {
        const data = await response.json();
        setDocentes(data);
      }
    } catch (error) {
      console.error('Error loading docentes:', error);
    }
  };

  const loadGrados = async () => {
    try {
      const response = await fetch('/api/grados', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (response.ok) {
        const data = await response.json();
        setGrados(data);
      }
    } catch (error) {
      console.error('Error loading grados:', error);
    }
  };

  const handleCreateAsignacion = async (data) => {
    try {
      const response = await fetch('/api/academic/asignaciones', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      });

      if (response.ok) {
        showNotification('Asignación creada exitosamente', 'success');
        loadAsignaciones();
        setShowForm(false);
        setFormData({});
      } else {
        const error = await response.json();
        showNotification(error.error || 'Error al crear asignación', 'error');
      }
    } catch (error) {
      console.error('Error creating asignacion:', error);
      showNotification('Error al crear asignación', 'error');
    }
  };

  const handleCreateMateria = async (data) => {
    try {
      const response = await fetch('/api/academic/materias', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      });

      if (response.ok) {
        showNotification('Materia creada exitosamente', 'success');
        loadMaterias();
        setShowForm(false);
        setFormData({});
      } else {
        const error = await response.json();
        showNotification(error.error || 'Error al crear materia', 'error');
      }
    } catch (error) {
      console.error('Error creating materia:', error);
      showNotification('Error al crear materia', 'error');
    }
  };

  const renderAsignaciones = () => {
    const diasSemana = ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes'];
    
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-900">Asignaciones Académicas</h3>
          {(user.rol === 'Superadministrador' || user.rol === 'Director' || user.rol === 'Coordinador Académico') && (
            <button
              onClick={() => {
                setActiveTab('asignaciones');
                setShowForm(true);
                setFormData({
                  docente_id: '',
                  materia_id: '',
                  grado_id: '',
                  horario_id: '',
                  aula: '',
                  periodo_academico: '2025',
                  fecha_inicio: new Date().toISOString().split('T')[0],
                  fecha_fin: '2025-12-31'
                });
              }}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Nueva Asignación
            </button>
          )}
        </div>

        {/* Horario semanal */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Hora
                  </th>
                  {diasSemana.map(dia => (
                    <th key={dia} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {dia}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {/* Generar filas por horas */}
                {Array.from(new Set(horarios.map(h => `${h.hora_inicio}-${h.hora_fin}`))).map(hora => (
                  <tr key={hora}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {hora}
                    </td>
                    {diasSemana.map((dia, diaIndex) => {
                      const diaNum = diaIndex + 1;
                      const asignacionesDia = asignaciones.filter(a => 
                        a.dia_semana === diaNum && 
                        `${a.hora_inicio}-${a.hora_fin}` === hora
                      );
                      
                      return (
                        <td key={`${dia}-${hora}`} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {asignacionesDia.map(asignacion => (
                            <div key={asignacion.id} className="mb-2 p-2 bg-blue-50 rounded border-l-4 border-blue-400">
                              <div className="font-medium text-blue-900">{asignacion.materia_nombre}</div>
                              <div className="text-xs text-blue-700">{asignacion.grado_nombre}</div>
                              <div className="text-xs text-blue-600">{asignacion.docente_nombre}</div>
                              {asignacion.aula && (
                                <div className="text-xs text-blue-600">Aula: {asignacion.aula}</div>
                              )}
                            </div>
                          ))}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  };

  const renderMaterias = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Materias</h3>
        {(user.rol === 'Superadministrador' || user.rol === 'Director' || user.rol === 'Coordinador Académico') && (
          <button
            onClick={() => {
              setActiveTab('materias');
              setShowForm(true);
              setFormData({
                nombre: '',
                codigo: '',
                descripcion: '',
                horas_semanales: 1
              });
            }}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Nueva Materia
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {materias.map(materia => (
          <div key={materia.id} className="academic-card bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <BookOpen className="w-8 h-8 text-green-600 mr-3" />
                <div>
                  <h4 className="font-semibold text-gray-900">{materia.nombre}</h4>
                  <p className="text-sm text-gray-500">{materia.codigo}</p>
                </div>
              </div>
            </div>
            
            <p className="text-gray-600 text-sm mb-4">{materia.descripcion}</p>
            
            <div className="flex justify-between items-center text-sm text-gray-500">
              <span>{materia.horas_semanales} hrs/semana</span>
              <span>{materia.asignaciones_activas || 0} asignaciones</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderFormAsignacion = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Nueva Asignación</h3>
          <button onClick={() => setShowForm(false)}>
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={(e) => {
          e.preventDefault();
          handleCreateAsignacion(formData);
        }} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Docente</label>
            <select
              value={formData.docente_id || ''}
              onChange={(e) => setFormData({...formData, docente_id: e.target.value})}
              className="w-full border border-gray-300 rounded-lg px-3 py-2"
              required
            >
              <option value="">Seleccionar docente</option>
              {docentes.map(docente => (
                <option key={docente.id} value={docente.id}>{docente.nombre}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Materia</label>
            <select
              value={formData.materia_id || ''}
              onChange={(e) => setFormData({...formData, materia_id: e.target.value})}
              className="w-full border border-gray-300 rounded-lg px-3 py-2"
              required
            >
              <option value="">Seleccionar materia</option>
              {materias.map(materia => (
                <option key={materia.id} value={materia.id}>{materia.nombre}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Grado</label>
            <select
              value={formData.grado_id || ''}
              onChange={(e) => setFormData({...formData, grado_id: e.target.value})}
              className="w-full border border-gray-300 rounded-lg px-3 py-2"
              required
            >
              <option value="">Seleccionar grado</option>
              {grados.map(grado => (
                <option key={grado.id} value={grado.id}>{grado.nombre}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Horario</label>
            <select
              value={formData.horario_id || ''}
              onChange={(e) => setFormData({...formData, horario_id: e.target.value})}
              className="w-full border border-gray-300 rounded-lg px-3 py-2"
              required
            >
              <option value="">Seleccionar horario</option>
              {horarios.map(horario => (
                <option key={horario.id} value={horario.id}>
                  {horario.dia_nombre} - {horario.nombre} ({horario.hora_inicio} - {horario.hora_fin})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Aula</label>
            <input
              type="text"
              value={formData.aula || ''}
              onChange={(e) => setFormData({...formData, aula: e.target.value})}
              className="w-full border border-gray-300 rounded-lg px-3 py-2"
              placeholder="Ej: Aula 101"
            />
          </div>

          <div className="flex gap-2">
            <button
              type="submit"
              className="flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700"
            >
              Crear Asignación
            </button>
            <button
              type="button"
              onClick={() => setShowForm(false)}
              className="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400"
            >
              Cancelar
            </button>
          </div>
        </form>
      </div>
    </div>
  );

  const renderFormMateria = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Nueva Materia</h3>
          <button onClick={() => setShowForm(false)}>
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={(e) => {
          e.preventDefault();
          handleCreateMateria(formData);
        }} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Nombre</label>
            <input
              type="text"
              value={formData.nombre || ''}
              onChange={(e) => setFormData({...formData, nombre: e.target.value})}
              className="w-full border border-gray-300 rounded-lg px-3 py-2"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Código</label>
            <input
              type="text"
              value={formData.codigo || ''}
              onChange={(e) => setFormData({...formData, codigo: e.target.value.toUpperCase()})}
              className="w-full border border-gray-300 rounded-lg px-3 py-2"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Descripción</label>
            <textarea
              value={formData.descripcion || ''}
              onChange={(e) => setFormData({...formData, descripcion: e.target.value})}
              className="w-full border border-gray-300 rounded-lg px-3 py-2"
              rows="3"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Horas por semana</label>
            <input
              type="number"
              min="1"
              max="10"
              value={formData.horas_semanales || 1}
              onChange={(e) => setFormData({...formData, horas_semanales: parseInt(e.target.value)})}
              className="w-full border border-gray-300 rounded-lg px-3 py-2"
              required
            />
          </div>

          <div className="flex gap-2">
            <button
              type="submit"
              className="flex-1 bg-green-600 text-white py-2 rounded-lg hover:bg-green-700"
            >
              Crear Materia
            </button>
            <button
              type="button"
              onClick={() => setShowForm(false)}
              className="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400"
            >
              Cancelar
            </button>
          </div>
        </form>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="academic-container space-y-6">
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('asignaciones')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'asignaciones'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Calendar className="w-4 h-4 inline mr-2" />
              Asignaciones
            </button>
            <button
              onClick={() => setActiveTab('materias')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'materias'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <BookOpen className="w-4 h-4 inline mr-2" />
              Materias
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'asignaciones' && renderAsignaciones()}
          {activeTab === 'materias' && renderMaterias()}
        </div>
      </div>

      {showForm && activeTab === 'asignaciones' && renderFormAsignacion()}
      {showForm && activeTab === 'materias' && renderFormMateria()}
    </div>
  );
};

export default GestionAcademica;
