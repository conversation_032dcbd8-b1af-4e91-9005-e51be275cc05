const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

// Función para probar endpoints
async function testEndpoints() {
  console.log('🧪 === PRUEBA DE ENDPOINTS ===\n');
  
  const tests = [
    {
      name: 'Health Check',
      method: 'GET',
      url: `${BASE_URL}/`,
      expectStatus: 200
    },
    {
      name: 'Database Test',
      method: 'GET',
      url: `${BASE_URL}/db-test`,
      expectStatus: 200
    },
    {
      name: 'Setup Status',
      method: 'GET',
      url: `${BASE_URL}/api/setup/status`,
      expectStatus: 200
    },
    {
      name: 'WaApi Config (sin auth - debe fallar)',
      method: 'GET',
      url: `${BASE_URL}/api/waapi/config`,
      expectStatus: 401
    },
    {
      name: 'Ausencias (sin auth - debe fallar)',
      method: 'GET',
      url: `${BASE_URL}/api/ausencias`,
      expectStatus: 401
    },
    {
      name: 'Dashboard Messages (sin auth - debe fallar)',
      method: 'GET',
      url: `${BASE_URL}/api/dashboard/messages`,
      expectStatus: 401
    }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      console.log(`🔍 Probando: ${test.name}`);
      
      const response = await axios({
        method: test.method,
        url: test.url,
        validateStatus: () => true // No lanzar error por códigos de estado
      });
      
      if (response.status === test.expectStatus) {
        console.log(`✅ ${test.name}: OK (${response.status})`);
        passedTests++;
      } else {
        console.log(`❌ ${test.name}: FAIL (esperado: ${test.expectStatus}, obtenido: ${response.status})`);
      }
      
    } catch (error) {
      console.log(`❌ ${test.name}: ERROR - ${error.message}`);
    }
    
    console.log(''); // Línea en blanco
  }
  
  console.log(`📊 RESULTADOS: ${passedTests}/${totalTests} pruebas pasaron`);
  
  if (passedTests === totalTests) {
    console.log('🎉 ¡Todos los endpoints están funcionando correctamente!');
  } else {
    console.log('⚠️  Algunos endpoints tienen problemas');
  }
  
  return passedTests === totalTests;
}

// Función para probar n8n
async function testN8n() {
  console.log('\n🤖 === PRUEBA DE N8N ===\n');
  
  const N8N_BASE_URL = 'http://localhost:5678';
  const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************.YHpG_nNe3ssUgOT7JmYm4a5o8VV221oQZHM3F4bIzzM';
  
  try {
    // Probar conexión básica
    console.log('🔍 Probando conexión con n8n...');
    const healthResponse = await axios.get(`${N8N_BASE_URL}/healthz`);
    console.log(`✅ n8n Health Check: OK (${healthResponse.status})`);
    
    // Probar API con autenticación
    console.log('🔍 Probando API de n8n...');
    const apiResponse = await axios.get(`${N8N_BASE_URL}/api/v1/workflows`, {
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY
      }
    });
    console.log(`✅ n8n API: OK (${apiResponse.status})`);
    
    // Listar workflows
    if (apiResponse.data.data) {
      console.log(`📊 Workflows encontrados: ${apiResponse.data.data.length}`);
      apiResponse.data.data.forEach((workflow, index) => {
        console.log(`   ${index + 1}. ${workflow.name} (${workflow.active ? 'Activo' : 'Inactivo'})`);
      });
    }
    
    // Probar webhook
    console.log('\n🔍 Probando webhook de IA...');
    try {
      const webhookResponse = await axios.post(`${N8N_BASE_URL}/webhook/whatsapp-ai`, {
        from: '+50312345678',
        body: 'Mensaje de prueba',
        name: 'Usuario Test'
      }, {
        timeout: 10000
      });
      console.log(`✅ Webhook IA: OK (${webhookResponse.status})`);
    } catch (webhookError) {
      console.log(`⚠️  Webhook IA: ${webhookError.response?.status || 'Error'} - Puede ser normal si no está configurado OpenAI`);
    }
    
    return true;
    
  } catch (error) {
    console.log(`❌ Error en n8n: ${error.message}`);
    return false;
  }
}

// Función principal
async function runAllTests() {
  console.log('🚀 === PRUEBAS COMPLETAS DEL SISTEMA CCJAP ===\n');
  
  const backendOk = await testEndpoints();
  const n8nOk = await testN8n();
  
  console.log('\n📋 === RESUMEN FINAL ===');
  console.log(`Backend: ${backendOk ? '✅ OK' : '❌ PROBLEMAS'}`);
  console.log(`n8n: ${n8nOk ? '✅ OK' : '❌ PROBLEMAS'}`);
  
  if (backendOk && n8nOk) {
    console.log('\n🎉 ¡SISTEMA COMPLETAMENTE FUNCIONAL!');
    console.log('\n📋 URLs importantes:');
    console.log('   Frontend: http://localhost:5173');
    console.log('   Backend: http://localhost:3001');
    console.log('   n8n: http://localhost:5678');
    console.log('   WhatsApp Dashboard: http://localhost:5173/whatsapp-dashboard');
    console.log('   Webhook IA: http://localhost:5678/webhook/whatsapp-ai');
  } else {
    console.log('\n⚠️  SISTEMA CON PROBLEMAS - Revisar logs');
  }
  
  return backendOk && n8nOk;
}

// Ejecutar si se llama directamente
if (require.main === module) {
  runAllTests()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Error en las pruebas:', error);
      process.exit(1);
    });
}

module.exports = { runAllTests, testEndpoints, testN8n };
