{"name": "@clerk/types", "version": "4.60.0", "description": "Typings for Clerk libraries.", "keywords": ["clerk", "react", "auth", "authentication", "passwordless", "session", "jwt", "types"], "homepage": "https://clerk.com/", "bugs": {"url": "https://github.com/clerk/javascript/issues"}, "repository": {"type": "git", "url": "git+https://github.com/clerk/javascript.git", "directory": "packages/types"}, "license": "MIT", "author": "Clerk", "main": "dist/index.js", "module": "dist/esm/index.js", "types": "dist/index.d.ts", "files": ["dist"], "dependencies": {"csstype": "3.1.3"}, "devDependencies": {}, "engines": {"node": ">=18.17.0"}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsup --env.NODE_ENV production", "build:declarations": "tsc -p tsconfig.declarations.json", "clean": "rimraf ./dist", "dev": "tsup --watch", "lint": "eslint src", "lint:attw": "attw --pack . --profile node16", "publish:local": "pnpm yalc push --replace  --sig"}}