import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

const GruposWhatsAppManager = ({ onGroupSelect, selectedGroup }) => {
  const [grupos, setGrupos] = useState([]);
  const [grados, setGrados] = useState([]);
  const [docentes, setDocentes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showSendMessage, setShowSendMessage] = useState(false);
  const [messageForm, setMessageForm] = useState({
    grupo_id: '',
    mensaje: '',
    tipo_mensaje: 'informativo'
  });
  const { token } = useAuth();

  useEffect(() => {
    fetchData();
  }, [token]);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      const [gruposRes, gradosRes, docentesRes] = await Promise.all([
        axios.get(`${API_BASE_URL}/api/grupos/whatsapp`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        axios.get(`${API_BASE_URL}/api/grupos/grados`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        axios.get(`${API_BASE_URL}/api/grupos/docentes`, {
          headers: { Authorization: `Bearer ${token}` }
        })
      ]);

      setGrupos(gruposRes.data);
      setGrados(gradosRes.data);
      setDocentes(docentesRes.data);
      
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    
    if (!messageForm.grupo_id || !messageForm.mensaje) {
      alert('Por favor complete todos los campos');
      return;
    }

    try {
      await axios.post(`${API_BASE_URL}/api/grupos/whatsapp/${messageForm.grupo_id}/enviar`, {
        mensaje: messageForm.mensaje,
        tipo_mensaje: messageForm.tipo_mensaje
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      alert('Mensaje enviado exitosamente');
      setMessageForm({ grupo_id: '', mensaje: '', tipo_mensaje: 'informativo' });
      setShowSendMessage(false);
      
    } catch (error) {
      console.error('Error sending message:', error);
      alert('Error enviando mensaje');
    }
  };

  const getGroupIcon = (tipo) => {
    switch (tipo) {
      case 'grado': return '👨‍👩‍👧‍👦';
      case 'docentes': return '👩‍🏫';
      case 'administrativo': return '🏢';
      case 'padres': return '👪';
      default: return '💬';
    }
  };

  const getGroupColor = (tipo) => {
    switch (tipo) {
      case 'grado': return 'bg-blue-100 text-blue-700 dark:bg-blue-700 dark:text-blue-100';
      case 'docentes': return 'bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100';
      case 'administrativo': return 'bg-purple-100 text-purple-700 dark:bg-purple-700 dark:text-purple-100';
      case 'padres': return 'bg-orange-100 text-orange-700 dark:bg-orange-700 dark:text-orange-100';
      default: return 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="p-4 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-2"></div>
        <p className="text-sm text-slate-600 dark:text-slate-400">Cargando grupos...</p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700">
      <div className="p-4 border-b border-slate-200 dark:border-slate-700">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200">
            Grupos de WhatsApp
          </h3>
          <button
            onClick={() => setShowSendMessage(!showSendMessage)}
            className="px-3 py-1 bg-indigo-600 text-white rounded-lg text-sm hover:bg-indigo-700 transition-colors"
          >
            {showSendMessage ? 'Cancelar' : 'Enviar Mensaje'}
          </button>
        </div>
      </div>

      {showSendMessage && (
        <div className="p-4 border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-700">
          <form onSubmit={handleSendMessage} className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                Seleccionar Grupo
              </label>
              <select
                value={messageForm.grupo_id}
                onChange={(e) => setMessageForm(prev => ({ ...prev, grupo_id: e.target.value }))}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
                required
              >
                <option value="">Seleccione un grupo...</option>
                {grupos.map(grupo => (
                  <option key={grupo.id} value={grupo.id}>
                    {getGroupIcon(grupo.tipo)} {grupo.nombre} ({grupo.total_miembros} miembros)
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                Tipo de Mensaje
              </label>
              <select
                value={messageForm.tipo_mensaje}
                onChange={(e) => setMessageForm(prev => ({ ...prev, tipo_mensaje: e.target.value }))}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
              >
                <option value="informativo">Informativo</option>
                <option value="urgente">Urgente</option>
                <option value="recordatorio">Recordatorio</option>
                <option value="evento">Evento</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                Mensaje
              </label>
              <textarea
                value={messageForm.mensaje}
                onChange={(e) => setMessageForm(prev => ({ ...prev, mensaje: e.target.value }))}
                placeholder="Escriba su mensaje aquí..."
                rows={4}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
                required
              />
            </div>

            <div className="flex space-x-2">
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Enviar Mensaje
              </button>
              <button
                type="button"
                onClick={() => setShowSendMessage(false)}
                className="px-4 py-2 bg-slate-500 text-white rounded-lg hover:bg-slate-600 transition-colors"
              >
                Cancelar
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {grupos.map(grupo => (
            <div
              key={grupo.id}
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                selectedGroup?.id === grupo.id
                  ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20'
                  : 'border-slate-200 dark:border-slate-600 hover:border-slate-300 dark:hover:border-slate-500'
              }`}
              onClick={() => onGroupSelect && onGroupSelect(grupo)}
            >
              <div className="flex items-center space-x-3 mb-2">
                <span className="text-2xl">{getGroupIcon(grupo.tipo)}</span>
                <div className="flex-1">
                  <h4 className="font-medium text-slate-700 dark:text-slate-200 text-sm">
                    {grupo.nombre}
                  </h4>
                  <p className="text-xs text-slate-500 dark:text-slate-400">
                    {grupo.grado_nombre || grupo.descripcion}
                  </p>
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <span className={`text-xs px-2 py-1 rounded-full font-medium ${getGroupColor(grupo.tipo)}`}>
                  {grupo.tipo.charAt(0).toUpperCase() + grupo.tipo.slice(1)}
                </span>
                <span className="text-xs text-slate-500 dark:text-slate-400">
                  {grupo.total_miembros} miembros
                </span>
              </div>
              
              {grupo.activo ? (
                <div className="mt-2 flex items-center text-xs text-green-600 dark:text-green-400">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                  Activo
                </div>
              ) : (
                <div className="mt-2 flex items-center text-xs text-red-600 dark:text-red-400">
                  <span className="w-2 h-2 bg-red-500 rounded-full mr-1"></span>
                  Inactivo
                </div>
              )}
            </div>
          ))}
        </div>

        {grupos.length === 0 && (
          <div className="text-center py-8 text-slate-500 dark:text-slate-400">
            <p>No hay grupos configurados</p>
            <p className="text-xs mt-1">Configure grupos de WhatsApp para comenzar</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default GruposWhatsAppManager;
