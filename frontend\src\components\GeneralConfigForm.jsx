import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { Save, RefreshCw, CheckCircle, AlertTriangle, Settings } from 'lucide-react';

const GeneralConfigForm = () => {
  const [formData, setFormData] = useState({
    timezone: 'America/El_Salvador',
    language: 'es',
    date_format: 'DD/MM/YYYY',
    time_format: '24h',
    academic_year_start: '',
    academic_year_end: '',
    enable_notifications: true,
    enable_email_notifications: true,
    enable_sms_notifications: false,
    max_students_per_class: 30,
    attendance_grace_period: 15
  });
  const [message, setMessage] = useState('');
  const [status, setStatus] = useState('idle'); // idle, loading, success, error
  const { token } = useAuth();

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setStatus('loading');
        const response = await fetch('/api/config', {
          headers: {
            Authorization: "Bearer " + token,
          },
        });
        if (!response.ok) {
          throw new Error("Error fetching config: " + response.status);
        }
        const data = await response.json();
        
        // Mapear las configuraciones del servidor al estado local
        const configs = data.configurations || {};
        setFormData({
          timezone: configs.timezone || 'America/El_Salvador',
          language: configs.language || 'es',
          date_format: configs.date_format || 'DD/MM/YYYY',
          time_format: configs.time_format || '24h',
          academic_year_start: configs.academic_year_start || '',
          academic_year_end: configs.academic_year_end || '',
          enable_notifications: configs.enable_notifications !== false,
          enable_email_notifications: configs.enable_email_notifications !== false,
          enable_sms_notifications: configs.enable_sms_notifications === true,
          max_students_per_class: configs.max_students_per_class || 30,
          attendance_grace_period: configs.attendance_grace_period || 15
        });
        setStatus('idle');
      } catch (error) {
        setMessage("Error al cargar la configuración: " + error.message);
        setStatus('error');
      }
    };

    fetchConfig();
  }, [token]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setStatus('loading');
      
      // Preparar las configuraciones para enviar al servidor
      const configurations = {};
      Object.entries(formData).forEach(([key, value]) => {
        let type = 'string';
        if (typeof value === 'boolean') {
          type = 'boolean';
        } else if (typeof value === 'number') {
          type = 'number';
        }
        
        configurations[key] = {
          value,
          type,
          description: getConfigDescription(key)
        };
      });

      const response = await fetch('/api/config/bulk', {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
        body: JSON.stringify({ configurations }),
      });
      
      if (!response.ok) {
        throw new Error("Error al guardar la configuración: " + response.status);
      }
      
      setMessage("Configuración general guardada con éxito!");
      setStatus('success');
      setTimeout(() => setStatus('idle'), 3000);
    } catch (error) {
      setMessage("Error al guardar la configuración: " + error.message);
      setStatus('error');
    }
  };

  const getConfigDescription = (key) => {
    const descriptions = {
      timezone: 'Zona horaria de la institución',
      language: 'Idioma predeterminado del sistema',
      date_format: 'Formato de fecha utilizado en el sistema',
      time_format: 'Formato de hora (12h o 24h)',
      academic_year_start: 'Fecha de inicio del año académico',
      academic_year_end: 'Fecha de fin del año académico',
      enable_notifications: 'Habilitar notificaciones del sistema',
      enable_email_notifications: 'Habilitar notificaciones por email',
      enable_sms_notifications: 'Habilitar notificaciones por SMS',
      max_students_per_class: 'Número máximo de estudiantes por clase',
      attendance_grace_period: 'Período de gracia para asistencia (minutos)'
    };
    return descriptions[key] || '';
  };

  return (
    <div className="p-6 bg-white dark:bg-slate-800 rounded-lg shadow-md">
      <div className="flex items-center mb-6">
        <Settings className="h-6 w-6 text-indigo-600 mr-2" />
        <h2 className="text-2xl font-bold text-slate-800 dark:text-white">Configuración General</h2>
      </div>
      
      {message && (
        <div className={`p-4 mb-6 rounded-md ${status === 'error' ? 'bg-red-50 text-red-700 border border-red-200' : 'bg-green-50 text-green-700 border border-green-200'}`}>
          {status === 'error' ? <AlertTriangle className="inline-block mr-2 h-5 w-5" /> : <CheckCircle className="inline-block mr-2 h-5 w-5" />}
          {message}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-md">
          <h3 className="text-lg font-semibold text-slate-700 dark:text-white mb-4">Configuración Regional</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="timezone" className="block text-sm font-medium text-slate-700 dark:text-gray-300 mb-1">
                Zona Horaria:
              </label>
              <select
                id="timezone"
                name="timezone"
                value={formData.timezone}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="America/El_Salvador">El Salvador (GMT-6)</option>
                <option value="America/Guatemala">Guatemala (GMT-6)</option>
                <option value="America/Honduras">Honduras (GMT-6)</option>
                <option value="America/Costa_Rica">Costa Rica (GMT-6)</option>
                <option value="America/Panama">Panamá (GMT-5)</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="language" className="block text-sm font-medium text-slate-700 dark:text-gray-300 mb-1">
                Idioma:
              </label>
              <select
                id="language"
                name="language"
                value={formData.language}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="es">Español</option>
                <option value="en">English</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="date_format" className="block text-sm font-medium text-slate-700 dark:text-gray-300 mb-1">
                Formato de Fecha:
              </label>
              <select
                id="date_format"
                name="date_format"
                value={formData.date_format}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                <option value="YYYY-MM-DD">YYYY-MM-DD</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="time_format" className="block text-sm font-medium text-slate-700 dark:text-gray-300 mb-1">
                Formato de Hora:
              </label>
              <select
                id="time_format"
                name="time_format"
                value={formData.time_format}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="24h">24 horas</option>
                <option value="12h">12 horas (AM/PM)</option>
              </select>
            </div>
          </div>
        </div>

        <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-md">
          <h3 className="text-lg font-semibold text-slate-700 dark:text-white mb-4">Año Académico</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="academic_year_start" className="block text-sm font-medium text-slate-700 dark:text-gray-300 mb-1">
                Inicio del Año Académico:
              </label>
              <input
                type="date"
                id="academic_year_start"
                name="academic_year_start"
                value={formData.academic_year_start}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            
            <div>
              <label htmlFor="academic_year_end" className="block text-sm font-medium text-slate-700 dark:text-gray-300 mb-1">
                Fin del Año Académico:
              </label>
              <input
                type="date"
                id="academic_year_end"
                name="academic_year_end"
                value={formData.academic_year_end}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
          </div>
        </div>

        <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-md">
          <h3 className="text-lg font-semibold text-slate-700 dark:text-white mb-4">Notificaciones</h3>
          
          <div className="space-y-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="enable_notifications"
                name="enable_notifications"
                checked={formData.enable_notifications}
                onChange={handleChange}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label htmlFor="enable_notifications" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Habilitar notificaciones del sistema
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="enable_email_notifications"
                name="enable_email_notifications"
                checked={formData.enable_email_notifications}
                onChange={handleChange}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label htmlFor="enable_email_notifications" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Habilitar notificaciones por email
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="enable_sms_notifications"
                name="enable_sms_notifications"
                checked={formData.enable_sms_notifications}
                onChange={handleChange}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label htmlFor="enable_sms_notifications" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Habilitar notificaciones por SMS
              </label>
            </div>
          </div>
        </div>

        <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-md">
          <h3 className="text-lg font-semibold text-slate-700 dark:text-white mb-4">Configuración Académica</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="max_students_per_class" className="block text-sm font-medium text-slate-700 dark:text-gray-300 mb-1">
                Máximo de Estudiantes por Clase:
              </label>
              <input
                type="number"
                id="max_students_per_class"
                name="max_students_per_class"
                value={formData.max_students_per_class}
                onChange={handleChange}
                min="1"
                max="100"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            
            <div>
              <label htmlFor="attendance_grace_period" className="block text-sm font-medium text-slate-700 dark:text-gray-300 mb-1">
                Período de Gracia para Asistencia (minutos):
              </label>
              <input
                type="number"
                id="attendance_grace_period"
                name="attendance_grace_period"
                value={formData.attendance_grace_period}
                onChange={handleChange}
                min="0"
                max="60"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
          </div>
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 flex items-center"
            disabled={status === 'loading'}
          >
            {status === 'loading' ? <RefreshCw className="animate-spin h-5 w-5 mr-2" /> : <Save className="h-5 w-5 mr-2" />}
            Guardar Configuración
          </button>
        </div>
      </form>
    </div>
  );
};

export default GeneralConfigForm;
