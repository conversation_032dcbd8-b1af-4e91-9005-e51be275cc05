const db = require('../config/db');

async function checkTableStructure() {
  try {
    console.log('=== Verificando estructura de tablas ===');
    
    // Verificar tabla alumnos
    const alumnosColumns = await db.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'alumnos' 
      ORDER BY ordinal_position
    `);
    
    console.log('\n📋 Columnas de la tabla alumnos:');
    alumnosColumns.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (${row.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });
    
    // Verificar tabla usuarios
    const usuariosColumns = await db.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'usuarios' 
      ORDER BY ordinal_position
    `);
    
    console.log('\n📋 Columnas de la tabla usuarios:');
    usuariosColumns.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (${row.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });
    
    // Verificar tabla grados
    const gradosColumns = await db.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'grados' 
      ORDER BY ordinal_position
    `);
    
    console.log('\n📋 Columnas de la tabla grados:');
    gradosColumns.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (${row.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });
    
    // Verificar datos existentes
    const alumnosCount = await db.query('SELECT COUNT(*) as count FROM alumnos');
    const usuariosCount = await db.query('SELECT COUNT(*) as count FROM usuarios');
    const gradosCount = await db.query('SELECT COUNT(*) as count FROM grados');
    
    console.log('\n📊 Datos existentes:');
    console.log(`  - Alumnos: ${alumnosCount.rows[0].count}`);
    console.log(`  - Usuarios: ${usuariosCount.rows[0].count}`);
    console.log(`  - Grados: ${gradosCount.rows[0].count}`);
    
  } catch (error) {
    console.error('❌ Error verificando estructura:', error);
  }
}

// Ejecutar
checkTableStructure()
  .then(() => process.exit(0))
  .catch(err => {
    console.error(err);
    process.exit(1);
  });
