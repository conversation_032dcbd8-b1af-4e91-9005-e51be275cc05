version: '3.8'

services:
  # Base de datos PostgreSQL
  postgres:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_USER: ccjap_admin
      POSTGRES_PASSWORD: K@rur0su24
      POSTGRES_DB: ccjap_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-postgres.sh:/docker-entrypoint-initdb.d/init-postgres.sh
    networks:
      - dokploy-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ccjap_admin"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    restart: always
    working_dir: /app
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=ccjap_db
      - DB_USER=ccjap_admin
      - DB_PASSWORD=K@rur0su24
      - JWT_SECRET=K@rur0su24_JWT_SECRET_2025
      - UPLOAD_DIR=/app/uploads
    volumes:
      - ./backend:/app
      - uploads:/app/uploads
    depends_on:
      - postgres
    networks:
      - dokploy-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ccjap-backend.rule=Host(`api.ccjap.echolab.xyz`)"
      - "traefik.http.routers.ccjap-backend.entrypoints=websecure"
      - "traefik.http.routers.ccjap-backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.ccjap-backend.loadbalancer.server.port=3001"

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    restart: always
    working_dir: /app
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=production
      - VITE_API_URL=https://api.ccjap.echolab.xyz
    networks:
      - dokploy-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ccjap-frontend.rule=Host(`ccjap.echolab.xyz`)"
      - "traefik.http.routers.ccjap-frontend.entrypoints=websecure"
      - "traefik.http.routers.ccjap-frontend.tls.certresolver=letsencrypt"
      - "traefik.http.services.ccjap-frontend.loadbalancer.server.port=5173"

  # n8n
  n8n:
    image: n8nio/n8n
    restart: always
    environment:
      - N8N_HOST=n8n.echolab.xyz
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - N8N_HOST_WHITELIST=n8n.echolab.xyz
      - N8N_ENCRYPTION_KEY=K@rur0su24_N8N_ENCRYPT_2025
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_USER=ccjap_admin
      - DB_POSTGRESDB_PASSWORD=K@rur0su24
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=K@rur0su24
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=false
    volumes:
      - n8n_data:/home/<USER>/.n8n
    networks:
      - dokploy-network
    depends_on:
      postgres:
        condition: service_healthy
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ccjap-n8n.rule=Host(`n8n.echolab.xyz`)"
      - "traefik.http.routers.ccjap-n8n.entrypoints=websecure"
      - "traefik.http.routers.ccjap-n8n.tls.certresolver=letsencrypt"
      - "traefik.http.services.ccjap-n8n.loadbalancer.server.port=5678"
    ports:
      - 5678:5678

networks:
  dokploy-network:
    external: true

volumes:
  postgres_data:
  uploads:
  n8n_data:
