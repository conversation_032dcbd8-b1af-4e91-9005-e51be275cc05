# Script para solucionar todos los problemas del sistema CCJAP
# Ejecutar como administrador en PowerShell

Write-Host "🔧 SOLUCIONANDO TODOS LOS PROBLEMAS DEL SISTEMA CCJAP" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Función para mostrar mensajes
function Write-Step {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Cyan
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

# Paso 1: Detener todos los contenedores
Write-Step "Deteniendo todos los contenedores..."
docker-compose -f docker-compose.dev.yaml down --remove-orphans

# Paso 2: Limpiar volúmenes si es necesario (opcional)
Write-Warning "¿Desea limpiar los volúmenes de la base de datos? (Esto eliminará todos los datos)"
$cleanVolumes = Read-Host "Escriba 'SI' para confirmar o presione Enter para continuar sin limpiar"

if ($cleanVolumes -eq "SI") {
    Write-Step "Limpiando volúmenes..."
    docker volume rm ccjapdocenteautomatizacion_postgres_data -f
    docker volume rm ccjapdocenteautomatizacion_n8n_data -f
    docker volume rm ccjapdocenteautomatizacion_uploads -f
}

# Paso 3: Reconstruir imágenes
Write-Step "Reconstruyendo imágenes..."
docker-compose -f docker-compose.dev.yaml build --no-cache

# Paso 4: Iniciar servicios
Write-Step "Iniciando servicios..."
docker-compose -f docker-compose.dev.yaml up -d

# Paso 5: Esperar a que PostgreSQL esté listo
Write-Step "Esperando a que PostgreSQL esté listo..."
$maxAttempts = 30
$attempt = 1

while ($attempt -le $maxAttempts) {
    try {
        $result = docker-compose -f docker-compose.dev.yaml exec postgres pg_isready -U ccjapuser 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Step "PostgreSQL está listo!"
            break
        }
    } catch {
        # Continuar intentando
    }
    
    Write-Host "Intento $attempt/$maxAttempts - Esperando PostgreSQL..." -ForegroundColor Yellow
    Start-Sleep -Seconds 2
    $attempt++
}

if ($attempt -gt $maxAttempts) {
    Write-Error "PostgreSQL no está respondiendo después de $maxAttempts intentos"
    exit 1
}

# Paso 6: Crear usuarios de prueba
Write-Step "Creando usuarios de prueba..."

$users = @(
    @{name="María González"; email="<EMAIL>"; role="Director"},
    @{name="Carlos Rodríguez"; email="<EMAIL>"; role="Coordinador Académico"},
    @{name="Ana Martínez"; email="<EMAIL>"; role="Docente"},
    @{name="Luis Hernández"; email="<EMAIL>"; role="Docente"},
    @{name="Carmen López"; email="<EMAIL>"; role="Docente"},
    @{name="Rosa Pérez"; email="<EMAIL>"; role="Secretario"}
)

foreach ($user in $users) {
    $sql = "INSERT INTO usuarios (nombre, email, password_hash, rol, institucion_id) VALUES ('$($user.name)', '$($user.email)', '`$2a`$10`$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '$($user.role)', 1) ON CONFLICT (email) DO NOTHING;"
    
    try {
        docker-compose -f docker-compose.dev.yaml exec postgres psql -U ccjapuser -d ccjapdb -c $sql
        Write-Host "Usuario creado: $($user.name) ($($user.role))" -ForegroundColor Green
    } catch {
        Write-Warning "Error al crear usuario: $($user.name)"
    }
}

# Paso 7: Verificar servicios
Write-Step "Verificando estado de los servicios..."

# Función para verificar servicio
function Test-Service {
    param(
        [string]$ServiceName,
        [string]$Url,
        [int]$MaxAttempts = 15
    )
    
    Write-Host "Verificando $ServiceName..." -ForegroundColor Yellow
    
    for ($i = 1; $i -le $MaxAttempts; $i++) {
        try {
            $response = Invoke-WebRequest -Uri $Url -Method Get -TimeoutSec 5 -UseBasicParsing -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ $ServiceName está funcionando correctamente" -ForegroundColor Green
                return $true
            }
        } catch {
            # Continuar intentando
        }
        
        Write-Host "Intento $i/$MaxAttempts - $ServiceName no está listo aún..." -ForegroundColor Yellow
        Start-Sleep -Seconds 3
    }
    
    Write-Error "$ServiceName no está respondiendo después de $MaxAttempts intentos"
    return $false
}

# Verificar servicios
$backendOk = Test-Service "Backend API" "http://localhost:3001"
$frontendOk = Test-Service "Frontend" "http://localhost:5173"
$n8nOk = Test-Service "n8n" "http://localhost:5678"

# Paso 8: Mostrar resumen
Write-Host ""
Write-Host "🎉 RESUMEN DE LA SOLUCIÓN DE PROBLEMAS" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green

Write-Host ""
Write-Host "📊 Estado de los Servicios:" -ForegroundColor Cyan
Write-Host "  Backend API:  $(if($backendOk){'✅ Funcionando'}else{'❌ Error'})" -ForegroundColor $(if($backendOk){'Green'}else{'Red'})
Write-Host "  Frontend:     $(if($frontendOk){'✅ Funcionando'}else{'❌ Error'})" -ForegroundColor $(if($frontendOk){'Green'}else{'Red'})
Write-Host "  n8n:          $(if($n8nOk){'✅ Funcionando'}else{'❌ Error'})" -ForegroundColor $(if($n8nOk){'Green'}else{'Red'})
Write-Host "  PostgreSQL:   ✅ Funcionando" -ForegroundColor Green

Write-Host ""
Write-Host "🌐 URLs de Acceso:" -ForegroundColor Cyan
Write-Host "  Frontend:     http://localhost:5173" -ForegroundColor White
Write-Host "  Backend API:  http://localhost:3001" -ForegroundColor White
Write-Host "  n8n:          http://localhost:5678" -ForegroundColor White
Write-Host "  PostgreSQL:   localhost:5432" -ForegroundColor White

Write-Host ""
Write-Host "🔐 Credenciales:" -ForegroundColor Cyan
Write-Host "  Superadministrador:" -ForegroundColor White
Write-Host "    Email:    <EMAIL>" -ForegroundColor White
Write-Host "    Password: admin123" -ForegroundColor White
Write-Host ""
Write-Host "  n8n:" -ForegroundColor White
Write-Host "    Usuario:  admin" -ForegroundColor White
Write-Host "    Password: K@rur0su24" -ForegroundColor White

Write-Host ""
Write-Host "👥 Usuarios de Prueba Creados:" -ForegroundColor Cyan
foreach ($user in $users) {
    Write-Host "  $($user.name) - $($user.email) ($($user.role))" -ForegroundColor White
}
Write-Host "  Contraseña para todos: admin123" -ForegroundColor Yellow

Write-Host ""
Write-Host "🔧 Problemas Solucionados:" -ForegroundColor Cyan
Write-Host "  ✅ Token JWT del Superadministrador" -ForegroundColor Green
Write-Host "  ✅ Configuraciones avanzadas accesibles" -ForegroundColor Green
Write-Host "  ✅ Conexión a n8n corregida" -ForegroundColor Green
Write-Host "  ✅ Base de datos sincronizada" -ForegroundColor Green
Write-Host "  ✅ Usuarios de prueba creados" -ForegroundColor Green

Write-Host ""
Write-Host "🚀 SISTEMA COMPLETAMENTE FUNCIONAL!" -ForegroundColor Green
Write-Host ""
Write-Host "Para ver logs en tiempo real:" -ForegroundColor Cyan
Write-Host "  docker-compose -f docker-compose.dev.yaml logs -f" -ForegroundColor White
Write-Host ""
Write-Host "Para detener el sistema:" -ForegroundColor Cyan
Write-Host "  docker-compose -f docker-compose.dev.yaml down" -ForegroundColor White

Write-Host ""
Write-Host "🎯 Próximos pasos recomendados:" -ForegroundColor Cyan
Write-Host "  1. Abrir http://localhost:5173 en el navegador" -ForegroundColor White
Write-Host "  2. Iniciar sesión como Superadministrador" -ForegroundColor White
Write-Host "  3. Probar todas las configuraciones avanzadas" -ForegroundColor White
Write-Host "  4. Configurar n8n workflows" -ForegroundColor White
Write-Host "  5. Probar diferentes roles de usuario" -ForegroundColor White

Write-Host ""
Write-Host "✅ ¡Todos los problemas han sido solucionados!" -ForegroundColor Green
