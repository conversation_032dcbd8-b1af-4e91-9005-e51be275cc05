/* #root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
} */

/* Comentamos o eliminamos los estilos por defecto de App.css 
   que no queremos que interfieran con Tailwind y nuestro layout.
   Podemos dejar algunos si son específicamente para elementos dentro de App.jsx 
   que no son parte del layout principal. Por ahora, los comentaremos.
*/

/*
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
*/

.input-class {
  background-color: white; /* or any other color you prefer */
  padding: 8px; /* Add some padding */
  appearance: none; /* Remove default browser styling */
  border: 1px solid #ccc; /* Add a border */
  border-radius: 4px; /* Add rounded corners */
  color: black; /* Set text color to black */
}
