const express = require('express');
const router = express.Router();
const db = require('../config/db');
const { authMiddleware, authorizeRoles } = require('../middleware/authMiddleware');

// GET /api/grupos/grados - Obtener todos los grados
router.get('/grados', authMiddleware, authorize<PERSON><PERSON>s('Director', 'Docente', 'Secretaria', 'Superadministrador', 'Director <PERSON><PERSON>'), async (req, res) => {
  try {
    const userInstitucionId = req.user.institucion_id;
    
    let query = `
      SELECT 
        g.*,
        COUNT(a.id) as total_alumnos,
        COUNT(CASE WHEN a.estado = 'Activo' THEN 1 END) as alumnos_activos,
        u.nombre as tutor_nombre
      FROM grados g
      LEFT JOIN alumnos a ON g.id = a.grado_id
      LEFT JOIN asignaciones_docente ad ON g.id = ad.grado_id AND ad.es_tutor = true AND ad.activo = true
      LEFT JOIN usuarios u ON ad.docente_id = u.id
    `;
    
    const values = [];
    
    if (req.user.rol !== 'Superadministrador') {
      query += ' WHERE g.institucion_id = $1';
      values.push(userInstitucionId);
    }
    
    query += ' GROUP BY g.id, u.nombre ORDER BY g.nivel, g.nombre';
    
    const result = await db.query(query, values);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error obteniendo grados:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// GET /api/grupos/whatsapp - Obtener grupos de WhatsApp
router.get('/whatsapp', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador', 'Director Academico'), async (req, res) => {
  try {
    const userInstitucionId = req.user.institucion_id;
    
    let query = `
      SELECT 
        gw.*,
        g.nombre as grado_nombre,
        COUNT(mgw.id) as total_miembros
      FROM grupos_whatsapp gw
      LEFT JOIN grados g ON gw.grado_id = g.id
      LEFT JOIN miembros_grupo_whatsapp mgw ON gw.id = mgw.grupo_id AND mgw.activo = true
    `;
    
    const values = [];
    
    if (req.user.rol !== 'Superadministrador') {
      query += ' WHERE gw.institucion_id = $1';
      values.push(userInstitucionId);
    }
    
    query += ' GROUP BY gw.id, g.nombre ORDER BY gw.tipo, gw.nombre';
    
    const result = await db.query(query, values);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error obteniendo grupos de WhatsApp:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// GET /api/grupos/asignaciones - Obtener asignaciones de docentes
router.get('/asignaciones', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador', 'Director Academico'), async (req, res) => {
  try {
    const userInstitucionId = req.user.institucion_id;
    
    let query = `
      SELECT 
        ad.*,
        u.nombre as docente_nombre,
        u.email as docente_email,
        u.telefono as docente_telefono,
        g.nombre as grado_nombre,
        g.nivel as grado_nivel,
        g.seccion as grado_seccion
      FROM asignaciones_docente ad
      JOIN usuarios u ON ad.docente_id = u.id
      JOIN grados g ON ad.grado_id = g.id
    `;
    
    const values = [];
    
    if (req.user.rol !== 'Superadministrador') {
      query += ' WHERE g.institucion_id = $1';
      values.push(userInstitucionId);
    }
    
    query += ' AND ad.activo = true ORDER BY g.nivel, g.nombre, ad.es_tutor DESC';
    
    const result = await db.query(query, values);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error obteniendo asignaciones:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/grupos/asignaciones - Crear nueva asignación
router.post('/asignaciones', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  try {
    const { docente_id, grado_id, materia, es_tutor } = req.body;
    
    if (!docente_id || !grado_id) {
      return res.status(400).json({ error: 'Docente y grado son requeridos' });
    }
    
    // Si es tutor, desactivar otros tutores del mismo grado
    if (es_tutor) {
      await db.query(
        'UPDATE asignaciones_docente SET es_tutor = false WHERE grado_id = $1 AND es_tutor = true',
        [grado_id]
      );
    }
    
    const result = await db.query(`
      INSERT INTO asignaciones_docente (docente_id, grado_id, materia, es_tutor)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (docente_id, grado_id, materia) 
      DO UPDATE SET es_tutor = $4, activo = true
      RETURNING id
    `, [docente_id, grado_id, materia, es_tutor]);
    
    res.status(201).json({
      success: true,
      message: 'Asignación creada exitosamente',
      id: result.rows[0].id
    });
    
  } catch (error) {
    console.error('Error creando asignación:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// DELETE /api/grupos/asignaciones/:id - Eliminar asignación
router.delete('/asignaciones/:id', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  try {
    const { id } = req.params;
    
    await db.query('UPDATE asignaciones_docente SET activo = false WHERE id = $1', [id]);
    
    res.json({
      success: true,
      message: 'Asignación eliminada exitosamente'
    });
    
  } catch (error) {
    console.error('Error eliminando asignación:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/grupos/whatsapp/:id/enviar - Enviar mensaje a grupo
router.post('/whatsapp/:id/enviar', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador', 'Director Academico'), async (req, res) => {
  try {
    const { id } = req.params;
    const { mensaje, tipo_mensaje } = req.body;
    
    if (!mensaje) {
      return res.status(400).json({ error: 'Mensaje es requerido' });
    }
    
    // Obtener información del grupo
    const grupoResult = await db.query(
      'SELECT * FROM grupos_whatsapp WHERE id = $1',
      [id]
    );
    
    if (grupoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Grupo no encontrado' });
    }
    
    const grupo = grupoResult.rows[0];
    
    // Obtener miembros del grupo
    const miembrosResult = await db.query(
      'SELECT telefono, nombre FROM miembros_grupo_whatsapp WHERE grupo_id = $1 AND activo = true',
      [id]
    );
    
    // Aquí integrarías con tu API de WhatsApp para enviar el mensaje
    // Por ahora solo guardamos el registro
    
    await db.query(`
      INSERT INTO mensajes_enviados (
        telefono_destinatario,
        contenido,
        tipo,
        usuario_id,
        institucion_id,
        grupo_id
      ) VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      'GRUPO_' + grupo.nombre,
      mensaje,
      tipo_mensaje || 'grupo',
      req.user.id,
      req.user.institucion_id,
      id
    ]);
    
    res.json({
      success: true,
      message: `Mensaje enviado a ${miembrosResult.rows.length} miembros del grupo ${grupo.nombre}`,
      destinatarios: miembrosResult.rows.length
    });
    
  } catch (error) {
    console.error('Error enviando mensaje a grupo:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// GET /api/grupos/docentes - Obtener lista de docentes para asignaciones
router.get('/docentes', authMiddleware, authorizeRoles('Director', 'Secretaria', 'Superadministrador'), async (req, res) => {
  try {
    const userInstitucionId = req.user.institucion_id;
    
    let query = `
      SELECT 
        id, 
        nombre, 
        email, 
        telefono,
        rol
      FROM usuarios 
      WHERE rol IN ('Docente', 'Director', 'Director Academico')
    `;
    
    const values = [];
    
    if (req.user.rol !== 'Superadministrador') {
      query += ' AND institucion_id = $1';
      values.push(userInstitucionId);
    }
    
    query += ' ORDER BY nombre';
    
    const result = await db.query(query, values);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error obteniendo docentes:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

module.exports = router;
