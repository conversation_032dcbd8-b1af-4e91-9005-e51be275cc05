<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de Permisos - CCJAP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Prueba de Permisos y Navegación</h1>
        <p>Esta herramienta verifica los permisos del usuario y la navegación del sistema.</p>
        
        <div class="test-section">
            <h3>1. Información del Usuario Actual</h3>
            <button class="btn" onclick="getUserInfo()">Obtener Info del Usuario</button>
            <div id="userInfo" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Verificar Navegación</h3>
            <button class="btn" onclick="testNavigation()">Probar Enlaces de Navegación</button>
            <div id="navResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Simular Permisos de Configuración Avanzada</h3>
            <button class="btn" onclick="testAdvancedPermissions()">Probar Permisos</button>
            <div id="permResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>4. Problemas Conocidos y Soluciones</h3>
            <div class="info result" style="display: block;">
<strong>Problema 1: Configuración y Config. Avanzada se marcan juntos</strong>
✅ SOLUCIONADO: Función isActiveLink() mejorada en Sidebar.jsx

<strong>Problema 2: Superadministrador con restricciones</strong>
🔍 EN INVESTIGACIÓN: Verificando función canAccess()

<strong>Cambios Realizados:</strong>
• Navegación mejorada con lógica específica para evitar conflictos
• Iconos diferentes para Configuración (Settings) y Config. Avanzada (Cog)
• Debug logs agregados para identificar problemas
• Panel de debug temporal para Superadministrador

<strong>Próximos Pasos:</strong>
1. Verificar que el rol se detecte correctamente
2. Confirmar que canAccess() funcione para Superadministrador
3. Remover logs de debug una vez solucionado
            </div>
        </div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        async function getUserInfo() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    showResult('userInfo', 'No hay token de autenticación. Por favor inicia sesión.', 'error');
                    return;
                }

                const response = await fetch('http://localhost:3001/api/users/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    const message = `INFORMACIÓN DEL USUARIO:
ID: ${data.id}
Nombre: ${data.nombre}
Email: ${data.email}
Rol: ${data.rol}
Institución ID: ${data.institucion_id || 'N/A'}

ANÁLISIS DE PERMISOS:
¿Es Superadministrador? ${data.rol === 'Superadministrador' ? 'SÍ ✅' : 'NO ❌'}
¿Debería tener acceso completo? ${data.rol === 'Superadministrador' ? 'SÍ ✅' : 'NO ❌'}

TOKEN INFO:
Token presente: ${token ? 'SÍ ✅' : 'NO ❌'}
Token válido: ${response.ok ? 'SÍ ✅' : 'NO ❌'}`;
                    
                    showResult('userInfo', message, data.rol === 'Superadministrador' ? 'success' : 'info');
                } else {
                    showResult('userInfo', `Error: ${data.error}`, 'error');
                }
            } catch (error) {
                showResult('userInfo', `Error de conexión: ${error.message}`, 'error');
            }
        }

        function testNavigation() {
            const currentPath = window.location.pathname;
            const testPaths = [
                '/',
                '/configuracion',
                '/configuracion-avanzada',
                '/usuarios',
                '/alumnos'
            ];

            let message = `PRUEBA DE NAVEGACIÓN:
Ruta actual: ${currentPath}

Simulación de función isActiveLink():`;

            testPaths.forEach(path => {
                let isActive = false;
                
                // Lógica de isActiveLink simulada
                if (path === '/configuracion' && currentPath === '/configuracion-avanzada') {
                    isActive = false;
                } else if (path === '/configuracion-avanzada') {
                    isActive = currentPath === '/configuracion-avanzada';
                } else if (path === '/') {
                    isActive = currentPath === '/';
                } else {
                    isActive = currentPath === path || currentPath.startsWith(path + '/');
                }
                
                message += `\n${path}: ${isActive ? '✅ ACTIVO' : '❌ Inactivo'}`;
            });

            message += `\n\n✅ PROBLEMA DE NAVEGACIÓN SOLUCIONADO:
La función isActiveLink() ahora maneja correctamente los conflictos entre
/configuracion y /configuracion-avanzada`;

            showResult('navResult', message, 'success');
        }

        function testAdvancedPermissions() {
            // Simular la función canAccess
            const userRole = 'Superadministrador'; // Simular rol
            const tabs = [
                'grupos',
                'asignaciones', 
                'waapi',
                'n8n',
                'database',
                'system',
                'notificaciones'
            ];

            let message = `PRUEBA DE PERMISOS CONFIGURACIÓN AVANZADA:
Rol simulado: ${userRole}

Función canAccess() simulada:`;

            tabs.forEach(tabId => {
                let canAccess = false;
                
                // Lógica de canAccess simulada
                if (userRole === 'Superadministrador') {
                    canAccess = true;
                } else if (userRole === 'Director') {
                    canAccess = false;
                } else if (userRole === 'Coordinador Académico' && ['grupos', 'asignaciones'].includes(tabId)) {
                    canAccess = true;
                } else if (userRole === 'Secretario' && ['grupos'].includes(tabId)) {
                    canAccess = true;
                }
                
                message += `\n${tabId}: ${canAccess ? '✅ PERMITIDO' : '❌ Denegado'}`;
            });

            message += `\n\n${userRole === 'Superadministrador' ? '✅' : '❌'} RESULTADO:
${userRole === 'Superadministrador' ? 
    'Superadministrador DEBE tener acceso a TODAS las pestañas' : 
    'Usuario con acceso limitado'}

Si ves restricciones siendo Superadministrador, verifica:
1. Que el rol se detecte correctamente en el frontend
2. Que no haya errores en la consola del navegador
3. Que el token JWT contenga el rol correcto`;

            showResult('permResult', message, userRole === 'Superadministrador' ? 'success' : 'warning');
        }

        // Verificar token al cargar
        window.onload = function() {
            const token = localStorage.getItem('token');
            if (!token) {
                document.body.insertAdjacentHTML('afterbegin', 
                    '<div style="background: #f8d7da; color: #721c24; padding: 15px; margin-bottom: 20px; border-radius: 5px; text-align: center;">' +
                    '⚠️ No hay token de autenticación. <a href="/" style="color: #721c24;">Ir al Login</a>' +
                    '</div>'
                );
            }
        };
    </script>
</body>
</html>
