# Script de despliegue para CCJAP Sistema de Gestión Docente (PowerShell)
# Uso: .\deploy.ps1 [dev|prod]

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "prod")]
    [string]$Environment
)

# Función para imprimir mensajes con colores
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "================================" -ForegroundColor Blue
    Write-Host $Message -ForegroundColor Blue
    Write-Host "================================" -ForegroundColor Blue
}

Write-Header "DESPLEGANDO CCJAP SISTEMA - ENTORNO: $Environment"

# Configurar archivos según el entorno
if ($Environment -eq "dev") {
    $ComposeFile = "docker-compose.dev.yaml"
    $EnvFile = ".env.dev"
    Write-Info "Configurando entorno de desarrollo..."
} elseif ($Environment -eq "prod") {
    $ComposeFile = "docker-compose.prod.yaml"
    $EnvFile = ".env.prod"
    Write-Info "Configurando entorno de producción..."
    
    # Verificar que existe el archivo de entorno de producción
    if (-not (Test-Path $EnvFile)) {
        Write-Error "Archivo $EnvFile no encontrado."
        Write-Info "Copia .env.prod.example a .env.prod y configura las variables."
        exit 1
    }
}

# Verificar que Docker está ejecutándose
try {
    docker info | Out-Null
} catch {
    Write-Error "Docker no está ejecutándose. Por favor, inicia Docker."
    exit 1
}

# Verificar que docker-compose está disponible
try {
    docker-compose --version | Out-Null
} catch {
    Write-Error "docker-compose no está instalado."
    exit 1
}

Write-Info "Verificando archivos necesarios..."

# Verificar que el archivo compose existe
if (-not (Test-Path $ComposeFile)) {
    Write-Error "Archivo $ComposeFile no encontrado."
    exit 1
}

Write-Info "Deteniendo contenedores existentes..."
docker-compose -f $ComposeFile down --remove-orphans

if ($Environment -eq "prod") {
    Write-Info "Construyendo imágenes para producción..."
    docker-compose -f $ComposeFile --env-file $EnvFile build --no-cache
} else {
    Write-Info "Construyendo imágenes para desarrollo..."
    docker-compose -f $ComposeFile build
}

Write-Info "Iniciando servicios..."
if ($Environment -eq "prod") {
    docker-compose -f $ComposeFile --env-file $EnvFile up -d
} else {
    docker-compose -f $ComposeFile up -d
}

Write-Info "Esperando que los servicios estén listos..."
Start-Sleep -Seconds 10

Write-Info "Verificando estado de los contenedores..."
docker-compose -f $ComposeFile ps

# Función para verificar servicio
function Test-Service {
    param(
        [string]$ServiceName,
        [string]$Url,
        [int]$MaxAttempts = 30
    )
    
    Write-Info "Verificando $ServiceName..."
    
    for ($attempt = 1; $attempt -le $MaxAttempts; $attempt++) {
        try {
            $response = Invoke-WebRequest -Uri $Url -Method Get -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Info "$ServiceName está funcionando correctamente ✓"
                return $true
            }
        } catch {
            # Continuar intentando
        }
        
        Write-Warning "Intento $attempt/$MaxAttempts - $ServiceName no está listo aún..."
        Start-Sleep -Seconds 2
    }
    
    Write-Error "$ServiceName no está respondiendo después de $MaxAttempts intentos"
    return $false
}

# URLs para verificar según el entorno
if ($Environment -eq "dev") {
    $BackendUrl = "http://localhost:3001"
    $FrontendUrl = "http://localhost:5173"
    $N8nUrl = "http://localhost:5678"
    
    # Verificar servicios en desarrollo
    Test-Service "Backend" $BackendUrl
    Test-Service "Frontend" $FrontendUrl
    Test-Service "n8n" $N8nUrl
}

Write-Header "DESPLIEGUE COMPLETADO"

if ($Environment -eq "dev") {
    Write-Info "🚀 Aplicación disponible en:"
    Write-Info "   Frontend: http://localhost:5173"
    Write-Info "   Backend:  http://localhost:3001"
    Write-Info "   n8n:      http://localhost:5678"
    Write-Info "   PostgreSQL: localhost:5432"
    Write-Host ""
    Write-Info "📋 Credenciales por defecto:"
    Write-Info "   Superadministrador: <EMAIL> / admin123"
    Write-Info "   n8n: admin / K@rur0su24"
} else {
    # Leer variables de entorno para producción
    $envContent = Get-Content $EnvFile | Where-Object { $_ -match "^[^#].*=" }
    $envVars = @{}
    foreach ($line in $envContent) {
        $parts = $line -split "=", 2
        if ($parts.Length -eq 2) {
            $envVars[$parts[0]] = $parts[1]
        }
    }
    
    Write-Info "🚀 Aplicación desplegada en producción:"
    Write-Info "   Frontend: https://$($envVars['FRONTEND_DOMAIN'])"
    Write-Info "   Backend:  https://$($envVars['BACKEND_DOMAIN'])"
    Write-Info "   n8n:      https://$($envVars['N8N_DOMAIN'])"
    Write-Host ""
    Write-Warning "⚠️  Asegúrate de que Traefik esté configurado correctamente"
    Write-Warning "⚠️  Verifica que los certificados SSL se generen automáticamente"
}

Write-Host ""
Write-Info "📊 Para ver logs en tiempo real:"
Write-Info "   docker-compose -f $ComposeFile logs -f"
Write-Host ""
Write-Info "🛑 Para detener la aplicación:"
Write-Info "   docker-compose -f $ComposeFile down"

Write-Info "✅ Despliegue completado exitosamente!"
