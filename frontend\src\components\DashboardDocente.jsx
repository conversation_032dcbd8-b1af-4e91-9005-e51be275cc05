import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { Calendar, Clock, Users, MessageSquare, AlertTriangle, BookOpen, FileText, X } from 'lucide-react';
import { showNotification } from '../utils/notifications';
import { fadeInUp, staggerIn } from '../utils/animations';

const DashboardDocente = () => {
  const { user, token } = useAuth();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    asignaciones: [],
    estadisticas: {
      total_asignaciones: 0,
      comunicaciones_pendientes: 0,
      ausencias_semana: 0
    }
  });
  const [comunicaciones, setComunicaciones] = useState([]);
  const [selectedAsignacion, setSelectedAsignacion] = useState(null);

  useEffect(() => {
    loadDashboardData();
    loadComunicaciones();
    
    // Aplicar animaciones
    setTimeout(() => {
      fadeInUp('.docente-container');
      staggerIn('.stat-card', 0.1);
      staggerIn('.asignacion-card', 0.1);
    }, 100);
  }, []);

  const loadDashboardData = async () => {
    try {
      const response = await fetch('/api/academic/dashboard/docente', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        setDashboardData(data);
      } else {
        showNotification('Error al cargar datos del dashboard', 'error');
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      showNotification('Error al cargar datos del dashboard', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadComunicaciones = async () => {
    try {
      const response = await fetch('/api/academic/comunicaciones?estado=pendiente', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        setComunicaciones(data);
      }
    } catch (error) {
      console.error('Error loading comunicaciones:', error);
    }
  };

  const handleResponderComunicacion = async (comunicacionId, respuesta) => {
    try {
      const response = await fetch(`/api/academic/comunicaciones/${comunicacionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          respuesta_manual: respuesta,
          estado: 'respondido'
        })
      });

      if (response.ok) {
        showNotification('Respuesta enviada exitosamente', 'success');
        loadComunicaciones();
        loadDashboardData(); // Actualizar estadísticas
      } else {
        showNotification('Error al enviar respuesta', 'error');
      }
    } catch (error) {
      console.error('Error responding to comunicacion:', error);
      showNotification('Error al enviar respuesta', 'error');
    }
  };

  const getDiaColor = (diaSemana) => {
    const colores = {
      1: 'bg-blue-100 text-blue-800',
      2: 'bg-green-100 text-green-800',
      3: 'bg-yellow-100 text-yellow-800',
      4: 'bg-purple-100 text-purple-800',
      5: 'bg-red-100 text-red-800'
    };
    return colores[diaSemana] || 'bg-gray-100 text-gray-800';
  };

  const getDiaNombre = (diaSemana) => {
    const dias = {
      1: 'Lunes',
      2: 'Martes',
      3: 'Miércoles',
      4: 'Jueves',
      5: 'Viernes',
      6: 'Sábado',
      7: 'Domingo'
    };
    return dias[diaSemana] || 'Desconocido';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="docente-container space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">Dashboard Docente</h1>
        <p className="text-blue-100">Bienvenido, {user.nombre}</p>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="stat-card bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100">
              <BookOpen className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Asignaciones Activas</p>
              <p className="text-2xl font-semibold text-gray-900">{dashboardData.estadisticas.total_asignaciones}</p>
            </div>
          </div>
        </div>

        <div className="stat-card bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100">
              <MessageSquare className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Comunicaciones Pendientes</p>
              <p className="text-2xl font-semibold text-gray-900">{dashboardData.estadisticas.comunicaciones_pendientes}</p>
            </div>
          </div>
        </div>

        <div className="stat-card bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-red-100">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Ausencias Esta Semana</p>
              <p className="text-2xl font-semibold text-gray-900">{dashboardData.estadisticas.ausencias_semana}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Mis Asignaciones */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Mis Asignaciones
          </h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {dashboardData.asignaciones.map(asignacion => (
              <div key={asignacion.id} className="asignacion-card border rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-semibold text-gray-900">{asignacion.materia_nombre}</h3>
                    <p className="text-sm text-gray-600">{asignacion.grado_nombre}</p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDiaColor(asignacion.dia_semana)}`}>
                    {getDiaNombre(asignacion.dia_semana)}
                  </span>
                </div>
                
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-2" />
                    {asignacion.hora_inicio} - {asignacion.hora_fin}
                  </div>
                  <div className="flex items-center">
                    <Users className="w-4 h-4 mr-2" />
                    {asignacion.total_alumnos} estudiantes
                  </div>
                  {asignacion.aula && (
                    <div className="flex items-center">
                      <FileText className="w-4 h-4 mr-2" />
                      {asignacion.aula}
                    </div>
                  )}
                </div>

                <button
                  onClick={() => setSelectedAsignacion(asignacion)}
                  className="mt-3 w-full bg-blue-50 text-blue-600 py-2 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  Ver Detalles
                </button>
              </div>
            ))}
          </div>

          {dashboardData.asignaciones.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <BookOpen className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No tienes asignaciones activas</p>
            </div>
          )}
        </div>
      </div>

      {/* Comunicaciones Pendientes */}
      {comunicaciones.length > 0 && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <MessageSquare className="w-5 h-5 mr-2" />
              Comunicaciones Pendientes
            </h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {comunicaciones.slice(0, 5).map(comunicacion => (
                <div key={comunicacion.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {comunicacion.alumno_nombre} {comunicacion.alumno_apellido}
                      </h4>
                      <p className="text-sm text-gray-600">{comunicacion.grado_nombre}</p>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium mt-1 ${
                        comunicacion.tipo_comunicacion === 'ausencia' ? 'bg-red-100 text-red-800' :
                        comunicacion.tipo_comunicacion === 'solicitud_tareas' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {comunicacion.tipo_comunicacion.replace('_', ' ')}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">
                      {new Date(comunicacion.created_at).toLocaleDateString()}
                    </span>
                  </div>
                  
                  <div className="mb-3">
                    <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded">
                      {comunicacion.mensaje_original}
                    </p>
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={() => {
                        const respuesta = prompt('Escribe tu respuesta:');
                        if (respuesta) {
                          handleResponderComunicacion(comunicacion.id, respuesta);
                        }
                      }}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm"
                    >
                      Responder
                    </button>
                    <button
                      onClick={() => handleResponderComunicacion(comunicacion.id, 'Mensaje recibido y procesado')}
                      className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 text-sm"
                    >
                      Marcar como Procesado
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Modal de detalles de asignación */}
      {selectedAsignacion && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Detalles de Asignación</h3>
              <button onClick={() => setSelectedAsignacion(null)}>
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Materia</label>
                <p className="text-gray-900">{selectedAsignacion.materia_nombre}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Grado</label>
                <p className="text-gray-900">{selectedAsignacion.grado_nombre}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Horario</label>
                <p className="text-gray-900">
                  {getDiaNombre(selectedAsignacion.dia_semana)} - {selectedAsignacion.hora_inicio} a {selectedAsignacion.hora_fin}
                </p>
              </div>
              
              {selectedAsignacion.aula && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Aula</label>
                  <p className="text-gray-900">{selectedAsignacion.aula}</p>
                </div>
              )}
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Estudiantes</label>
                <p className="text-gray-900">{selectedAsignacion.total_alumnos} estudiantes</p>
              </div>
            </div>

            <div className="mt-6 flex gap-2">
              <button
                onClick={() => {
                  // Aquí podrías redirigir a una página de gestión de la clase
                  showNotification('Funcionalidad en desarrollo', 'info');
                }}
                className="flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700"
              >
                Gestionar Clase
              </button>
              <button
                onClick={() => setSelectedAsignacion(null)}
                className="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400"
              >
                Cerrar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardDocente;
