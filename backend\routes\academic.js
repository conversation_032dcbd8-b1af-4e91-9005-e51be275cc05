const express = require('express');
const router = express.Router();
const db = require('../config/db');
const { authMiddleware, authorizeRoles } = require('../middleware/authMiddleware');

// ============================================================================
// RUTAS DE MATERIAS
// ============================================================================

// GET /api/academic/materias - Obtener todas las materias
router.get('/materias', authMiddleware, authorizeRoles('Superadministrador', 'Director', 'Coordinador Académico', 'Docente'), async (req, res) => {
  try {
    const institucionId = req.user.rol === 'Superadministrador' ? req.query.institucion_id || 1 : req.user.institucion_id;
    
    const result = await db.query(
      `SELECT m.*, 
       COUNT(aa.id) as asignaciones_activas
       FROM materias m
       LEFT JOIN asignaciones_academicas aa ON m.id = aa.materia_id AND aa.activo = true
       WHERE m.institucion_id = $1 AND m.activo = true
       GROUP BY m.id
       ORDER BY m.nombre`,
      [institucionId]
    );
    
    res.json(result.rows);
  } catch (err) {
    console.error('Error al obtener materias:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/academic/materias - Crear nueva materia
router.post('/materias', authMiddleware, authorizeRoles('Superadministrador', 'Director', 'Coordinador Académico'), async (req, res) => {
  try {
    const { nombre, codigo, descripcion, horas_semanales } = req.body;
    const institucionId = req.user.rol === 'Superadministrador' ? req.body.institucion_id || 1 : req.user.institucion_id;
    
    const result = await db.query(
      `INSERT INTO materias (nombre, codigo, descripcion, horas_semanales, institucion_id)
       VALUES ($1, $2, $3, $4, $5) RETURNING *`,
      [nombre, codigo, descripcion, horas_semanales, institucionId]
    );
    
    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error('Error al crear materia:', err.stack);
    if (err.code === '23505') {
      res.status(400).json({ error: 'El código de materia ya existe' });
    } else {
      res.status(500).json({ error: 'Error interno del servidor' });
    }
  }
});

// ============================================================================
// RUTAS DE HORARIOS
// ============================================================================

// GET /api/academic/horarios - Obtener horarios
router.get('/horarios', authMiddleware, authorizeRoles('Superadministrador', 'Director', 'Coordinador Académico', 'Docente'), async (req, res) => {
  try {
    const institucionId = req.user.rol === 'Superadministrador' ? req.query.institucion_id || 1 : req.user.institucion_id;
    
    const result = await db.query(
      `SELECT h.*,
       CASE h.dia_semana
         WHEN 1 THEN 'Lunes'
         WHEN 2 THEN 'Martes'
         WHEN 3 THEN 'Miércoles'
         WHEN 4 THEN 'Jueves'
         WHEN 5 THEN 'Viernes'
         WHEN 6 THEN 'Sábado'
         WHEN 7 THEN 'Domingo'
       END as dia_nombre
       FROM horarios h
       WHERE h.institucion_id = $1 AND h.activo = true
       ORDER BY h.dia_semana, h.hora_inicio`,
      [institucionId]
    );
    
    res.json(result.rows);
  } catch (err) {
    console.error('Error al obtener horarios:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// ============================================================================
// RUTAS DE ASIGNACIONES ACADÉMICAS
// ============================================================================

// GET /api/academic/asignaciones - Obtener asignaciones académicas
router.get('/asignaciones', authMiddleware, authorizeRoles('Superadministrador', 'Director', 'Coordinador Académico', 'Docente'), async (req, res) => {
  try {
    const institucionId = req.user.rol === 'Superadministrador' ? req.query.institucion_id || 1 : req.user.institucion_id;
    
    let query = `
      SELECT aa.*,
             u.nombre as docente_nombre,
             m.nombre as materia_nombre,
             m.codigo as materia_codigo,
             g.nombre as grado_nombre,
             h.nombre as horario_nombre,
             h.hora_inicio,
             h.hora_fin,
             h.dia_semana,
             CASE h.dia_semana
               WHEN 1 THEN 'Lunes'
               WHEN 2 THEN 'Martes'
               WHEN 3 THEN 'Miércoles'
               WHEN 4 THEN 'Jueves'
               WHEN 5 THEN 'Viernes'
               WHEN 6 THEN 'Sábado'
               WHEN 7 THEN 'Domingo'
             END as dia_nombre
      FROM asignaciones_academicas aa
      JOIN usuarios u ON aa.docente_id = u.id
      JOIN materias m ON aa.materia_id = m.id
      JOIN grados g ON aa.grado_id = g.id
      JOIN horarios h ON aa.horario_id = h.id
      WHERE aa.activo = true
    `;
    
    const params = [];
    
    // Filtrar por institución
    if (req.user.rol !== 'Superadministrador') {
      query += ` AND u.institucion_id = $${params.length + 1}`;
      params.push(institucionId);
    }
    
    // Filtrar por docente si es un docente
    if (req.user.rol === 'Docente') {
      query += ` AND aa.docente_id = $${params.length + 1}`;
      params.push(req.user.id);
    }
    
    // Filtrar por grado si se especifica
    if (req.query.grado_id) {
      query += ` AND aa.grado_id = $${params.length + 1}`;
      params.push(req.query.grado_id);
    }
    
    query += ` ORDER BY h.dia_semana, h.hora_inicio, g.nombre`;
    
    const result = await db.query(query, params);
    res.json(result.rows);
  } catch (err) {
    console.error('Error al obtener asignaciones:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// POST /api/academic/asignaciones - Crear nueva asignación
router.post('/asignaciones', authMiddleware, authorizeRoles('Superadministrador', 'Director', 'Coordinador Académico'), async (req, res) => {
  try {
    const { docente_id, materia_id, grado_id, horario_id, aula, periodo_academico, fecha_inicio, fecha_fin } = req.body;
    
    // Verificar que no haya conflictos de horario
    const conflictCheck = await db.query(
      `SELECT aa.*, u.nombre as docente_nombre, h.nombre as horario_nombre
       FROM asignaciones_academicas aa
       JOIN usuarios u ON aa.docente_id = u.id
       JOIN horarios h ON aa.horario_id = h.id
       WHERE aa.docente_id = $1 AND aa.horario_id = $2 
       AND aa.activo = true
       AND (
         ($3 BETWEEN aa.fecha_inicio AND aa.fecha_fin) OR
         ($4 BETWEEN aa.fecha_inicio AND aa.fecha_fin) OR
         (aa.fecha_inicio BETWEEN $3 AND $4)
       )`,
      [docente_id, horario_id, fecha_inicio, fecha_fin]
    );
    
    if (conflictCheck.rows.length > 0) {
      return res.status(400).json({ 
        error: 'Conflicto de horario detectado',
        conflicto: conflictCheck.rows[0]
      });
    }
    
    const result = await db.query(
      `INSERT INTO asignaciones_academicas 
       (docente_id, materia_id, grado_id, horario_id, aula, periodo_academico, fecha_inicio, fecha_fin)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
      [docente_id, materia_id, grado_id, horario_id, aula, periodo_academico, fecha_inicio, fecha_fin]
    );
    
    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error('Error al crear asignación:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// ============================================================================
// RUTAS DE COMUNICACIONES CON PADRES
// ============================================================================

// GET /api/academic/comunicaciones - Obtener comunicaciones con padres
router.get('/comunicaciones', authMiddleware, authorizeRoles('Superadministrador', 'Director', 'Coordinador Académico', 'Secretario', 'Docente'), async (req, res) => {
  try {
    const institucionId = req.user.rol === 'Superadministrador' ? req.query.institucion_id || 1 : req.user.institucion_id;
    
    let query = `
      SELECT cp.*,
             a.nombre as alumno_nombre,
             a.apellido as alumno_apellido,
             g.nombre as grado_nombre,
             u.nombre as procesado_por_nombre
      FROM comunicaciones_padres cp
      JOIN alumnos a ON cp.alumno_id = a.id
      JOIN grados g ON a.grado_id = g.id
      LEFT JOIN usuarios u ON cp.procesado_por = u.id
      WHERE a.institucion_id = $1
    `;
    
    const params = [institucionId];
    
    // Filtrar por estado si se especifica
    if (req.query.estado) {
      query += ` AND cp.estado = $${params.length + 1}`;
      params.push(req.query.estado);
    }
    
    // Filtrar por tipo si se especifica
    if (req.query.tipo) {
      query += ` AND cp.tipo_comunicacion = $${params.length + 1}`;
      params.push(req.query.tipo);
    }
    
    // Si es docente, solo ver comunicaciones de sus alumnos
    if (req.user.rol === 'Docente') {
      query += ` AND EXISTS (
        SELECT 1 FROM asignaciones_academicas aa 
        WHERE aa.docente_id = $${params.length + 1} 
        AND aa.grado_id = a.grado_id 
        AND aa.activo = true
      )`;
      params.push(req.user.id);
    }
    
    query += ` ORDER BY cp.created_at DESC LIMIT 100`;
    
    const result = await db.query(query, params);
    res.json(result.rows);
  } catch (err) {
    console.error('Error al obtener comunicaciones:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// PUT /api/academic/comunicaciones/:id - Actualizar comunicación
router.put('/comunicaciones/:id', authMiddleware, authorizeRoles('Superadministrador', 'Director', 'Coordinador Académico', 'Secretario', 'Docente'), async (req, res) => {
  try {
    const { id } = req.params;
    const { respuesta_manual, estado } = req.body;
    
    const result = await db.query(
      `UPDATE comunicaciones_padres 
       SET respuesta_manual = $1, estado = $2, procesado_por = $3, fecha_procesamiento = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
       WHERE id = $4 RETURNING *`,
      [respuesta_manual, estado, req.user.id, id]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Comunicación no encontrada' });
    }
    
    res.json(result.rows[0]);
  } catch (err) {
    console.error('Error al actualizar comunicación:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// ============================================================================
// RUTAS DE DASHBOARD PARA DOCENTES
// ============================================================================

// GET /api/academic/dashboard/docente - Dashboard específico para docentes
router.get('/dashboard/docente', authMiddleware, authorizeRoles('Docente'), async (req, res) => {
  try {
    const docenteId = req.user.id;
    
    // Obtener asignaciones del docente
    const asignaciones = await db.query(
      `SELECT aa.*,
              m.nombre as materia_nombre,
              g.nombre as grado_nombre,
              h.nombre as horario_nombre,
              h.hora_inicio,
              h.hora_fin,
              h.dia_semana,
              COUNT(DISTINCT a.id) as total_alumnos
       FROM asignaciones_academicas aa
       JOIN materias m ON aa.materia_id = m.id
       JOIN grados g ON aa.grado_id = g.id
       JOIN horarios h ON aa.horario_id = h.id
       LEFT JOIN alumnos a ON a.grado_id = aa.grado_id AND a.activo = true
       WHERE aa.docente_id = $1 AND aa.activo = true
       GROUP BY aa.id, m.nombre, g.nombre, h.nombre, h.hora_inicio, h.hora_fin, h.dia_semana
       ORDER BY h.dia_semana, h.hora_inicio`,
      [docenteId]
    );
    
    // Obtener comunicaciones pendientes
    const comunicacionesPendientes = await db.query(
      `SELECT COUNT(*) as total
       FROM comunicaciones_padres cp
       JOIN alumnos a ON cp.alumno_id = a.id
       WHERE cp.estado = 'pendiente'
       AND EXISTS (
         SELECT 1 FROM asignaciones_academicas aa 
         WHERE aa.docente_id = $1 
         AND aa.grado_id = a.grado_id 
         AND aa.activo = true
       )`,
      [docenteId]
    );
    
    // Obtener ausencias recientes
    const ausenciasRecientes = await db.query(
      `SELECT COUNT(*) as total
       FROM reportes_ausencias ra
       JOIN alumnos a ON ra.alumno_id = a.id
       WHERE ra.fecha_ausencia >= CURRENT_DATE - INTERVAL '7 days'
       AND EXISTS (
         SELECT 1 FROM asignaciones_academicas aa 
         WHERE aa.docente_id = $1 
         AND aa.grado_id = a.grado_id 
         AND aa.activo = true
       )`,
      [docenteId]
    );
    
    res.json({
      asignaciones: asignaciones.rows,
      estadisticas: {
        total_asignaciones: asignaciones.rows.length,
        comunicaciones_pendientes: parseInt(comunicacionesPendientes.rows[0].total),
        ausencias_semana: parseInt(ausenciasRecientes.rows[0].total)
      }
    });
  } catch (err) {
    console.error('Error al obtener dashboard docente:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

module.exports = router;
