@tailwind base;
@tailwind components;
@tailwind utilities;

html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark; /* Allows browser to use its own dark/light theme */
  /* background-color: #242424; /* Default dark, will be overridden by Tailwind on components */
  /* color: rgba(255, 255, 255, 0.87); */

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-width: 320px; /* Keep for very small screens */
}

/* Estilos base para inputs usando @apply de Tailwind */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="number"],
input[type="date"],
input[type="datetime-local"],
input[type="time"],
select,
textarea {
  @apply bg-white text-slate-700 border border-slate-300 rounded-md shadow-sm
         px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500
         dark:bg-slate-700 dark:text-dark-text dark:border-dark-border;
}

/* Estilos específicos para inputs de fecha */
input[type="date"],
input[type="datetime-local"],
input[type="time"] {
  @apply relative;
  color-scheme: light;
  background-color: white !important;
  color: #374151 !important;
  min-height: 2.5rem;
}

/* Asegurar que el icono del calendario sea visible */
input[type="date"]::-webkit-calendar-picker-indicator,
input[type="datetime-local"]::-webkit-calendar-picker-indicator,
input[type="time"]::-webkit-calendar-picker-indicator {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor' aria-hidden='true'%3e%3cpath fill-rule='evenodd' d='M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z' clip-rule='evenodd'/%3e%3c/svg%3e");
  background-size: 1.25rem 1.25rem;
  background-repeat: no-repeat;
  background-position: center;
  width: 1.5rem;
  height: 1.5rem;
  cursor: pointer;
  opacity: 0.7;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover,
input[type="datetime-local"]::-webkit-calendar-picker-indicator:hover,
input[type="time"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
}

/* Para modo oscuro */
.dark input[type="date"],
.dark input[type="datetime-local"],
.dark input[type="time"] {
  color-scheme: dark;
  background-color: #374151 !important;
  color: #f9fafb !important;
}

.dark input[type="date"]::-webkit-calendar-picker-indicator,
.dark input[type="datetime-local"]::-webkit-calendar-picker-indicator,
.dark input[type="time"]::-webkit-calendar-picker-indicator {
  filter: invert(1);
}


/* Default Vite styles that might be useful to keep or adjust */
:root {
  /* These are now mostly handled by Tailwind or body styles */
}

a {
  font-weight: 500;
  /* color: #646cff; */ /* Let Tailwind handle link colors or define globally */
  text-decoration: inherit;
}
/* a:hover {
  color: #535bf2;
} */

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  /* background-color: #1a1a1a; */ /* Eliminado para que Tailwind controle el fondo */
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

/* Keep dark mode preference but let Tailwind manage actual colors */
/* @media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
} */
