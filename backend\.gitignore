# Dependencias
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Variables de entorno
.env

# Archivos de entorno locales
.env.local
.env.development.local
.env.test.local
.env.production.local

# Directorio de subidas
/uploads
!/uploads/.gitkeep

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Archivos del sistema operativo
.DS_Store
Thumbs.db

# Archivos de configuración local
.eslintcache

# Directorio de cobertura
yarn-error.log

# Directorio de dependencias
.pnp
.pnp.js

# Testing
/coverage

# Production
/build
/dist

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local development
.env.local
.env.development.local
.env.test.local
.env.production.local

# Ver el archivo .env.example para ver las variables de entorno necesarias
