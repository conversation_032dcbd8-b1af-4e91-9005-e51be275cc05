<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crear Superadministrador - CCJAP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            background: #218838;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .credentials {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Crear Superadministrador</h1>
        <p>Esta herramienta crea o actualiza el usuario Superadministrador del sistema.</p>
        
        <div id="status" class="status"></div>
        
        <div>
            <button class="btn" onclick="createSuperadmin()">
                👑 Crear/Actualizar Superadministrador
            </button>
            
            <button class="btn btn-danger" onclick="clearStorage()">
                🗑️ Limpiar Storage
            </button>
        </div>
        
        <div class="credentials">
            <h3>📋 Credenciales del Superadministrador:</h3>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Contraseña:</strong> admin123</p>
            <p><strong>Rol:</strong> Superadministrador</p>
        </div>
        
        <div style="margin-top: 30px; text-align: left;">
            <h3>¿Qué hace esta herramienta?</h3>
            <ul>
                <li>Verifica si el Superadministrador existe</li>
                <li>Si no existe, lo crea con las credenciales por defecto</li>
                <li>Si existe, actualiza su contraseña</li>
                <li>Configura el rol como "Superadministrador"</li>
            </ul>
            
            <h3>¿Cuándo usarla?</h3>
            <ul>
                <li>Cuando aparece "Credenciales inválidas" en el login</li>
                <li>Después de resetear la base de datos</li>
                <li>Para solucionar problemas de autenticación</li>
                <li>En la configuración inicial del sistema</li>
            </ul>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.innerHTML = message;
            status.style.display = 'block';
        }

        async function createSuperadmin() {
            try {
                showStatus('🔄 Creando superadministrador...', 'info');
                
                const response = await fetch('http://localhost:3001/api/auth/create-superadmin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus(`
                        ✅ <strong>${data.message}</strong><br><br>
                        👤 Usuario: ${data.user.nombre}<br>
                        📧 Email: ${data.user.email}<br>
                        🎭 Rol: ${data.user.rol}<br><br>
                        <strong>¡Ahora puedes hacer login!</strong><br>
                        <a href="/" style="color: #007bff;">Ir al Login</a>
                    `, 'success');
                } else {
                    showStatus(`❌ Error: ${data.error}`, 'error');
                }
                
            } catch (error) {
                console.error('Error:', error);
                showStatus(`❌ Error de conexión: ${error.message}`, 'error');
            }
        }

        function clearStorage() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                showStatus('✅ Storage limpiado exitosamente.', 'success');
                console.log('🧹 Storage limpiado');
            } catch (error) {
                console.error('Error al limpiar storage:', error);
                showStatus(`❌ Error al limpiar storage: ${error.message}`, 'error');
            }
        }

        // Mostrar información inicial
        window.onload = function() {
            showStatus(`
                📋 <strong>Instrucciones:</strong><br>
                1. Haz clic en "Crear/Actualizar Superadministrador"<br>
                2. Espera la confirmación<br>
                3. Ve al login e inicia sesión<br>
                4. Si hay problemas, limpia el storage primero
            `, 'info');
        };
    </script>
</body>
</html>
