import React, { useState } from 'react';
import InstitucionConfigForm from '../components/InstitucionConfigForm';
import GeneralConfigForm from '../components/GeneralConfigForm';
import WaApiConfigForm from '../components/WaApiConfigForm';
import N8nWorkflowManager from '../components/N8nWorkflowManager';
import { Building, Settings, MessageSquare, Workflow } from 'lucide-react';

const ConfiguracionPage = () => {
  const [activeTab, setActiveTab] = useState('institucion');

  const tabs = [
    {
      id: 'institucion',
      name: 'Institución',
      icon: Building,
      component: InstitucionConfigForm
    },
    {
      id: 'general',
      name: 'General',
      icon: Settings,
      component: GeneralConfigForm
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      icon: MessageSquare,
      component: WaApiConfigForm
    },
    {
      id: 'workflows',
      name: 'Workflows',
      icon: Workflow,
      component: N8nWorkflowManager
    }
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component;

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-slate-700 dark:text-white mb-2">Configuración</h1>
        <p className="text-slate-600 dark:text-gray-300">Gestiona la configuración de tu institución y del sistema.</p>
      </div>

      {/* Tabs Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`group inline-flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Icon className={`mr-2 h-5 w-5 ${
                  activeTab === tab.id
                    ? 'text-indigo-500 dark:text-indigo-400'
                    : 'text-gray-400 group-hover:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-400'
                }`} />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {ActiveComponent && <ActiveComponent />}
      </div>
    </div>
  );
};

export default ConfiguracionPage;
