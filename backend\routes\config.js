const express = require('express');
const router = express.Router();
const db = require('../config/db');
const { authMiddleware, authorizeRoles } = require('../middleware/authMiddleware');

// GET /api/config - Obtener todas las configuraciones de la institución
router.get('/', authMiddleware, authorizeR<PERSON>s('Director', 'Superadministrador'), async (req, res) => {
  try {
    // Obtener configuraciones generales
    const configResult = await db.query(
      'SELECT config_key, config_value, config_type FROM configurations WHERE institucion_id = $1', 
      [req.user.institucion_id]
    );
    
    // Obtener información de la institución
    const institucionResult = await db.query(
      'SELECT nombre, logo_url FROM instituciones WHERE id = $1', 
      [req.user.institucion_id]
    );

    // Convertir configuraciones a objeto
    const configurations = {};
    configResult.rows.forEach(row => {
      let value = row.config_value;
      
      // Convertir el valor según el tipo
      switch (row.config_type) {
        case 'boolean':
          value = value === 'true';
          break;
        case 'number':
          value = parseFloat(value);
          break;
        case 'json':
          try {
            value = JSON.parse(value);
          } catch (e) {
            console.error('Error parsing JSON config:', e);
          }
          break;
        default:
          // string - no conversion needed
          break;
      }
      
      configurations[row.config_key] = value;
    });

    const response = {
      institucion: institucionResult.rows[0] || {},
      configurations
    };

    res.json(response);
  } catch (err) {
    console.error('Error al obtener configuraciones:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor al obtener configuraciones', details: err.message });
  }
});

// POST /api/config/institucion - Actualizar información de la institución
router.post('/institucion', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  const { nombre, logo_url } = req.body;

  if (!nombre) {
    return res.status(400).json({ error: 'El nombre de la institución es requerido.' });
  }

  try {
    await db.query(
      'UPDATE instituciones SET nombre = $1, logo_url = $2 WHERE id = $3',
      [nombre, logo_url || null, req.user.institucion_id]
    );

    res.json({ message: 'Información de la institución actualizada con éxito.' });
  } catch (err) {
    console.error('Error al actualizar institución:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor al actualizar institución', details: err.message });
  }
});

// POST /api/config/setting - Guardar o actualizar una configuración específica
router.post('/setting', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  const { config_key, config_value, config_type = 'string', description } = req.body;

  if (!config_key) {
    return res.status(400).json({ error: 'La clave de configuración es requerida.' });
  }

  try {
    // Verificar si la configuración ya existe
    const existingConfig = await db.query(
      'SELECT 1 FROM configurations WHERE institucion_id = $1 AND config_key = $2', 
      [req.user.institucion_id, config_key]
    );

    let valueToStore = config_value;
    
    // Convertir el valor según el tipo para almacenamiento
    switch (config_type) {
      case 'boolean':
        valueToStore = config_value ? 'true' : 'false';
        break;
      case 'number':
        valueToStore = config_value.toString();
        break;
      case 'json':
        valueToStore = JSON.stringify(config_value);
        break;
      default:
        // string - no conversion needed
        valueToStore = config_value;
        break;
    }

    if (existingConfig.rows.length > 0) {
      // Actualizar configuración existente
      await db.query(
        'UPDATE configurations SET config_value = $1, config_type = $2, description = $3 WHERE institucion_id = $4 AND config_key = $5',
        [valueToStore, config_type, description || null, req.user.institucion_id, config_key]
      );
    } else {
      // Crear nueva configuración
      await db.query(
        'INSERT INTO configurations (institucion_id, config_key, config_value, config_type, description) VALUES ($1, $2, $3, $4, $5)',
        [req.user.institucion_id, config_key, valueToStore, config_type, description || null]
      );
    }

    res.json({ message: 'Configuración guardada con éxito.' });
  } catch (err) {
    console.error('Error al guardar configuración:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor al guardar configuración', details: err.message });
  }
});

// POST /api/config/bulk - Guardar múltiples configuraciones
router.post('/bulk', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  const { configurations } = req.body;

  if (!configurations || typeof configurations !== 'object') {
    return res.status(400).json({ error: 'Las configuraciones son requeridas y deben ser un objeto.' });
  }

  const client = await db.pool.connect();
  
  try {
    await client.query('BEGIN');

    for (const [config_key, configData] of Object.entries(configurations)) {
      const { value, type = 'string', description } = configData;
      
      let valueToStore = value;
      
      // Convertir el valor según el tipo para almacenamiento
      switch (type) {
        case 'boolean':
          valueToStore = value ? 'true' : 'false';
          break;
        case 'number':
          valueToStore = value.toString();
          break;
        case 'json':
          valueToStore = JSON.stringify(value);
          break;
        default:
          valueToStore = value;
          break;
      }

      // Usar UPSERT (INSERT ... ON CONFLICT)
      await client.query(`
        INSERT INTO configurations (institucion_id, config_key, config_value, config_type, description) 
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (institucion_id, config_key) 
        DO UPDATE SET 
          config_value = EXCLUDED.config_value,
          config_type = EXCLUDED.config_type,
          description = EXCLUDED.description,
          updated_at = CURRENT_TIMESTAMP
      `, [req.user.institucion_id, config_key, valueToStore, type, description || null]);
    }

    await client.query('COMMIT');
    res.json({ message: 'Configuraciones guardadas con éxito.' });
  } catch (err) {
    await client.query('ROLLBACK');
    console.error('Error al guardar configuraciones en lote:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor al guardar configuraciones', details: err.message });
  } finally {
    client.release();
  }
});

// DELETE /api/config/setting/:key - Eliminar una configuración específica
router.delete('/setting/:key', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  const { key } = req.params;

  try {
    const result = await db.query(
      'DELETE FROM configurations WHERE institucion_id = $1 AND config_key = $2',
      [req.user.institucion_id, key]
    );

    if (result.rowCount === 0) {
      return res.status(404).json({ error: 'Configuración no encontrada.' });
    }

    res.json({ message: 'Configuración eliminada con éxito.' });
  } catch (err) {
    console.error('Error al eliminar configuración:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor al eliminar configuración', details: err.message });
  }
});

module.exports = router;
