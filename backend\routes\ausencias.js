const express = require('express');
const router = express.Router();
const db = require('../config/db');
const { authMiddleware, authorizeRoles } = require('../middleware/authMiddleware');

// GET /api/ausencias - Obtener todas las ausencias (con filtro por institución)
router.get('/', authMiddleware, authorize<PERSON><PERSON>s('Director', 'Docente', 'Secretaria', 'Superadministrador', 'Director <PERSON><PERSON>'), async (req, res) => {
  try {
    const userRol = req.user.rol;
    const userInstitucionId = req.user.institucion_id;
    const userId = req.user.id;
    
    let query = `
      SELECT 
        a.*, 
        al.nombre_completo as alumno_nombre, 
        al.grado_actual,
        u.nombre as docente_nombre,
        COALESCE(a.fecha_ausencia, a.fecha_registro::date) as fecha_ausencia_display
      FROM ausencias a
      LEFT JOIN alumnos al ON a.alumno_id = al.id
      LEFT JOIN usuarios u ON al.maestro_id = u.id
    `;
    
    const values = [];
    
    // Filtrar por institución si no es superadmin
    if (userRol !== 'Superadministrador') {
      if (userInstitucionId) {
        query += ' WHERE al.institucion_id = $1';
        values.push(userInstitucionId);
        
        // Si es Docente, mostrar solo las ausencias de sus alumnos
        if (userRol === 'Docente') {
          query += ' AND al.maestro_id = $2';
          values.push(userId);
        }
      } else {
        // Si no tiene institución asignada, no mostrar nada
        query += ' WHERE 1=0';
      }
    }
    
    query += ' ORDER BY COALESCE(a.fecha_ausencia, a.fecha_registro::date) DESC';
    
    const result = await db.query(query, values);
    
    // Formatear los datos para el frontend
    const ausencias = result.rows.map(row => ({
      id: row.id,
      alumno_id: row.alumno_id,
      alumno_nombre: row.alumno_nombre || 'Estudiante no identificado',
      grado_actual: row.grado_actual || row.grado_reportado,
      fecha_ausencia: row.fecha_ausencia_display,
      motivo: row.motivo,
      justificado: row.justificado,
      notificado_docente: row.notificado_docente,
      confirmado_recibido: row.confirmado_recibido,
      docente_nombre: row.docente_nombre,
      reportado_por_telefono: row.reportado_por_telefono,
      reportado_por_nombre: row.reportado_por_nombre,
      fecha_registro: row.fecha_registro
    }));
    
    res.json(ausencias);
  } catch (err) {
    console.error('Error al obtener ausencias:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor', details: err.message });
  }
});

// POST /api/ausencias - Crear nueva ausencia
router.post('/', authMiddleware, authorizeRoles('Director', 'Docente', 'Secretaria', 'Superadministrador'), async (req, res) => {
  try {
    const { alumno_id, fecha_ausencia, motivo, justificado = false } = req.body;
    
    if (!alumno_id || !fecha_ausencia) {
      return res.status(400).json({ error: 'Alumno y fecha de ausencia son requeridos' });
    }
    
    // Verificar que el alumno existe y pertenece a la institución del usuario
    const alumnoQuery = `
      SELECT id, institucion_id, maestro_id 
      FROM alumnos 
      WHERE id = $1 ${req.user.rol !== 'Superadministrador' ? 'AND institucion_id = $2' : ''}
    `;
    
    const alumnoValues = req.user.rol !== 'Superadministrador' 
      ? [alumno_id, req.user.institucion_id]
      : [alumno_id];
    
    const alumnoResult = await db.query(alumnoQuery, alumnoValues);
    
    if (alumnoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Alumno no encontrado o no pertenece a su institución' });
    }
    
    // Crear la ausencia
    const insertQuery = `
      INSERT INTO ausencias (
        alumno_id, 
        fecha_ausencia, 
        motivo, 
        justificado,
        reportado_por_usuario_id
      ) VALUES ($1, $2, $3, $4, $5) 
      RETURNING id
    `;
    
    const result = await db.query(insertQuery, [
      alumno_id,
      fecha_ausencia,
      motivo,
      justificado,
      req.user.id
    ]);
    
    res.status(201).json({
      success: true,
      message: 'Ausencia registrada exitosamente',
      ausencia_id: result.rows[0].id
    });
    
  } catch (err) {
    console.error('Error al crear ausencia:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor', details: err.message });
  }
});

// PUT /api/ausencias/:id - Actualizar ausencia
router.put('/:id', authMiddleware, authorizeRoles('Director', 'Docente', 'Secretaria', 'Superadministrador'), async (req, res) => {
  try {
    const { id } = req.params;
    const { motivo, justificado, confirmado_recibido } = req.body;
    
    // Verificar que la ausencia existe y el usuario tiene permisos
    const checkQuery = `
      SELECT a.id 
      FROM ausencias a
      LEFT JOIN alumnos al ON a.alumno_id = al.id
      WHERE a.id = $1 ${req.user.rol !== 'Superadministrador' ? 'AND al.institucion_id = $2' : ''}
    `;
    
    const checkValues = req.user.rol !== 'Superadministrador' 
      ? [id, req.user.institucion_id]
      : [id];
    
    const checkResult = await db.query(checkQuery, checkValues);
    
    if (checkResult.rows.length === 0) {
      return res.status(404).json({ error: 'Ausencia no encontrada o no tiene permisos para modificarla' });
    }
    
    // Actualizar la ausencia
    const updateQuery = `
      UPDATE ausencias 
      SET motivo = COALESCE($1, motivo),
          justificado = COALESCE($2, justificado),
          confirmado_recibido = COALESCE($3, confirmado_recibido),
          fecha_actualizacion = CURRENT_TIMESTAMP
      WHERE id = $4
    `;
    
    await db.query(updateQuery, [motivo, justificado, confirmado_recibido, id]);
    
    res.json({
      success: true,
      message: 'Ausencia actualizada exitosamente'
    });
    
  } catch (err) {
    console.error('Error al actualizar ausencia:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor', details: err.message });
  }
});

// DELETE /api/ausencias/:id - Eliminar ausencia
router.delete('/:id', authMiddleware, authorizeRoles('Director', 'Superadministrador'), async (req, res) => {
  try {
    const { id } = req.params;
    
    // Verificar que la ausencia existe y el usuario tiene permisos
    const checkQuery = `
      SELECT a.id 
      FROM ausencias a
      LEFT JOIN alumnos al ON a.alumno_id = al.id
      WHERE a.id = $1 ${req.user.rol !== 'Superadministrador' ? 'AND al.institucion_id = $2' : ''}
    `;
    
    const checkValues = req.user.rol !== 'Superadministrador' 
      ? [id, req.user.institucion_id]
      : [id];
    
    const checkResult = await db.query(checkQuery, checkValues);
    
    if (checkResult.rows.length === 0) {
      return res.status(404).json({ error: 'Ausencia no encontrada o no tiene permisos para eliminarla' });
    }
    
    // Eliminar la ausencia
    await db.query('DELETE FROM ausencias WHERE id = $1', [id]);
    
    res.json({
      success: true,
      message: 'Ausencia eliminada exitosamente'
    });
    
  } catch (err) {
    console.error('Error al eliminar ausencia:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor', details: err.message });
  }
});

// GET /api/ausencias/stats - Obtener estadísticas de ausencias
router.get('/stats', authMiddleware, authorizeRoles('Director', 'Docente', 'Secretaria', 'Superadministrador'), async (req, res) => {
  try {
    const userRol = req.user.rol;
    const userInstitucionId = req.user.institucion_id;
    const userId = req.user.id;
    
    let whereClause = '';
    const values = [];
    
    if (userRol !== 'Superadministrador') {
      if (userInstitucionId) {
        whereClause = 'WHERE al.institucion_id = $1';
        values.push(userInstitucionId);
        
        if (userRol === 'Docente') {
          whereClause += ' AND al.maestro_id = $2';
          values.push(userId);
        }
      } else {
        whereClause = 'WHERE 1=0';
      }
    }
    
    const statsQuery = `
      SELECT 
        COUNT(*) as total_ausencias,
        COUNT(CASE WHEN DATE(COALESCE(a.fecha_ausencia, a.fecha_registro)) = CURRENT_DATE THEN 1 END) as ausencias_hoy,
        COUNT(CASE WHEN a.justificado = true THEN 1 END) as ausencias_justificadas,
        COUNT(CASE WHEN a.notificado_docente = true THEN 1 END) as docentes_notificados
      FROM ausencias a
      LEFT JOIN alumnos al ON a.alumno_id = al.id
      ${whereClause}
    `;
    
    const result = await db.query(statsQuery, values);
    
    res.json({
      total_ausencias: parseInt(result.rows[0].total_ausencias),
      ausencias_hoy: parseInt(result.rows[0].ausencias_hoy),
      ausencias_justificadas: parseInt(result.rows[0].ausencias_justificadas),
      docentes_notificados: parseInt(result.rows[0].docentes_notificados)
    });
    
  } catch (err) {
    console.error('Error al obtener estadísticas de ausencias:', err.stack);
    res.status(500).json({ error: 'Error interno del servidor', details: err.message });
  }
});

module.exports = router;
