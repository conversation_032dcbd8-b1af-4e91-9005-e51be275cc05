const db = require('../config/db');

async function checkMessagesTable() {
  try {
    const result = await db.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'mensajes_whatsapp' 
      ORDER BY ordinal_position
    `);
    
    console.log('Columnas de mensajes_whatsapp:');
    result.rows.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type}`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  }
}

checkMessagesTable().then(() => process.exit(0));
