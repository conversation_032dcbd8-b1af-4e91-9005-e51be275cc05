const bcrypt = require('bcryptjs');
const { Pool } = require('pg');

// Configuración de la base de datos
const pool = new Pool({
  user: 'ccjapuser',
  host: 'localhost',
  database: 'ccjapdb',
  password: 'ccjappassword',
  port: 5432,
});

async function createSuperadmin() {
  const client = await pool.connect();
  
  try {
    console.log('🔍 Verificando superadministrador...');
    
    // 1. Verificar si existe
    const checkResult = await client.query(
      'SELECT * FROM usuarios WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (checkResult.rows.length > 0) {
      console.log('✅ Superadministrador encontrado:', checkResult.rows[0]);
      
      // Verificar la contraseña
      const user = checkResult.rows[0];
      const isValidPassword = await bcrypt.compare('admin123', user.password_hash);
      console.log('🔐 Contraseña válida:', isValidPassword);
      
      if (!isValidPassword) {
        console.log('🔄 Actualizando contraseña...');
        const newPasswordHash = await bcrypt.hash('admin123', 10);
        await client.query(
          'UPDATE usuarios SET password_hash = $1 WHERE email = $2',
          [newPasswordHash, '<EMAIL>']
        );
        console.log('✅ Contraseña actualizada');
      }
    } else {
      console.log('❌ Superadministrador no encontrado. Creando...');
      
      // 2. Crear el superadministrador
      const passwordHash = await bcrypt.hash('admin123', 10);
      
      const insertResult = await client.query(
        `INSERT INTO usuarios (nombre, email, password_hash, rol, institucion_id) 
         VALUES ($1, $2, $3, $4, $5) 
         RETURNING *`,
        ['Super Administrador', '<EMAIL>', passwordHash, 'Superadministrador', null]
      );
      
      console.log('✅ Superadministrador creado:', insertResult.rows[0]);
    }
    
    // 3. Mostrar todos los usuarios para debug
    const allUsers = await client.query('SELECT id, nombre, email, rol FROM usuarios ORDER BY created_at');
    console.log('\n📊 Todos los usuarios en la base de datos:');
    allUsers.rows.forEach(user => {
      console.log(`  - ${user.nombre} (${user.email}) - ${user.rol}`);
    });
    
    // 4. Probar el login
    console.log('\n🧪 Probando login...');
    const loginUser = await client.query(
      'SELECT * FROM usuarios WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (loginUser.rows.length > 0) {
      const user = loginUser.rows[0];
      const isValidPassword = await bcrypt.compare('admin123', user.password_hash);
      console.log('✅ Login test exitoso:', isValidPassword);
      
      if (isValidPassword) {
        console.log('\n🎉 ¡SUPERADMINISTRADOR LISTO PARA USAR!');
        console.log('📧 Email: <EMAIL>');
        console.log('🔑 Password: admin123');
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Ejecutar el script
createSuperadmin().catch(console.error);
