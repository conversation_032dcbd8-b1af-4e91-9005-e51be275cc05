import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../context/AuthContext';
import { 
  MessageCircle, 
  Bell, 
  User, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  FileText,
  Send,
  Volume2,
  VolumeX,
  Settings,
  Filter,
  Search
} from 'lucide-react';

const WhatsAppDashboard = () => {
  const [messages, setMessages] = useState([]);
  const [filteredMessages, setFilteredMessages] = useState([]);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [loading, setLoading] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [filter, setFilter] = useState('all'); // all, pending, processed, absence
  const [searchTerm, setSearchTerm] = useState('');
  const [newMessageCount, setNewMessageCount] = useState(0);
  const [replyText, setReplyText] = useState('');
  const [sending, setSending] = useState(false);
  
  const { token } = useAuth();
  const audioRef = useRef(null);
  const wsRef = useRef(null);

  // Sonido de notificación
  useEffect(() => {
    audioRef.current = new Audio('/notification.mp3');
    audioRef.current.volume = 0.5;
  }, []);

  // WebSocket para notificaciones en tiempo real
  useEffect(() => {
    if (!token) return;

    const connectWebSocket = () => {
      const wsUrl = `ws://localhost:3001/ws?token=${token}`;
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket conectado');
      };

      wsRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        if (data.type === 'new_whatsapp_message') {
          // Nuevo mensaje recibido
          setMessages(prev => [data.message, ...prev]);
          setNewMessageCount(prev => prev + 1);
          
          // Reproducir sonido si está habilitado
          if (soundEnabled && audioRef.current) {
            audioRef.current.play().catch(e => console.log('Error playing sound:', e));
          }
          
          // Mostrar notificación del navegador
          if (Notification.permission === 'granted') {
            new Notification('Nuevo mensaje de WhatsApp', {
              body: `${data.message.sender_name}: ${data.message.content.substring(0, 50)}...`,
              icon: '/whatsapp-icon.png'
            });
          }
        }
      };

      wsRef.current.onclose = () => {
        console.log('WebSocket desconectado, reintentando...');
        setTimeout(connectWebSocket, 3000);
      };

      wsRef.current.onerror = (error) => {
        console.error('Error en WebSocket:', error);
      };
    };

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [token, soundEnabled]);

  // Solicitar permisos de notificación
  useEffect(() => {
    if (Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  // Cargar mensajes iniciales
  useEffect(() => {
    fetchMessages();
  }, [token]);

  // Filtrar mensajes
  useEffect(() => {
    let filtered = messages;

    if (filter !== 'all') {
      filtered = messages.filter(msg => {
        switch (filter) {
          case 'pending':
            return !msg.processed;
          case 'processed':
            return msg.processed;
          case 'absence':
            return msg.message_type === 'absence';
          case 'question':
            return msg.message_type === 'question';
          default:
            return true;
        }
      });
    }

    if (searchTerm) {
      filtered = filtered.filter(msg => 
        msg.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        msg.sender_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        msg.phone_number.includes(searchTerm)
      );
    }

    setFilteredMessages(filtered);
  }, [messages, filter, searchTerm]);

  const fetchMessages = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/whatsapp/messages', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setMessages(data);
      }
    } catch (error) {
      console.error('Error cargando mensajes:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsProcessed = async (messageId) => {
    try {
      const response = await fetch(`/api/whatsapp/messages/${messageId}/process`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        }
      });

      if (response.ok) {
        setMessages(prev => 
          prev.map(msg => 
            msg.id === messageId ? { ...msg, processed: true } : msg
          )
        );
      }
    } catch (error) {
      console.error('Error marcando mensaje como procesado:', error);
    }
  };

  const sendReply = async () => {
    if (!selectedMessage || !replyText.trim()) return;

    try {
      setSending(true);
      const response = await fetch('/api/whatsapp/send-reply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          phone_number: selectedMessage.phone_number,
          message: replyText,
          reply_to: selectedMessage.id
        })
      });

      if (response.ok) {
        setReplyText('');
        // Actualizar la conversación
        fetchMessages();
      }
    } catch (error) {
      console.error('Error enviando respuesta:', error);
    } finally {
      setSending(false);
    }
  };

  const getMessageTypeColor = (type) => {
    switch (type) {
      case 'absence':
        return 'bg-red-100 text-red-800';
      case 'question':
        return 'bg-blue-100 text-blue-800';
      case 'complaint':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getMessageTypeIcon = (type) => {
    switch (type) {
      case 'absence':
        return <AlertTriangle className="w-4 h-4" />;
      case 'question':
        return <MessageCircle className="w-4 h-4" />;
      case 'complaint':
        return <FileText className="w-4 h-4" />;
      default:
        return <MessageCircle className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Panel izquierdo - Lista de mensajes */}
      <div className="w-1/3 bg-white border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <MessageCircle className="w-6 h-6 mr-2 text-green-600" />
              WhatsApp Dashboard
              {newMessageCount > 0 && (
                <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-1">
                  {newMessageCount}
                </span>
              )}
            </h2>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setSoundEnabled(!soundEnabled)}
                className={`p-2 rounded-lg ${soundEnabled ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}
              >
                {soundEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
              </button>
              <button className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200">
                <Settings className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Búsqueda */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Buscar mensajes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
          </div>

          {/* Filtros */}
          <div className="flex space-x-2">
            {[
              { key: 'all', label: 'Todos', count: messages.length },
              { key: 'pending', label: 'Pendientes', count: messages.filter(m => !m.processed).length },
              { key: 'absence', label: 'Ausencias', count: messages.filter(m => m.message_type === 'absence').length },
              { key: 'question', label: 'Preguntas', count: messages.filter(m => m.message_type === 'question').length }
            ].map(({ key, label, count }) => (
              <button
                key={key}
                onClick={() => setFilter(key)}
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  filter === key
                    ? 'bg-indigo-100 text-indigo-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {label} ({count})
              </button>
            ))}
          </div>
        </div>

        {/* Lista de mensajes */}
        <div className="flex-1 overflow-y-auto">
          {filteredMessages.map((message) => (
            <div
              key={message.id}
              onClick={() => setSelectedMessage(message)}
              className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                selectedMessage?.id === message.id ? 'bg-indigo-50 border-indigo-200' : ''
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <User className="w-4 h-4 text-gray-400" />
                    <span className="font-medium text-gray-900">
                      {message.sender_name || message.phone_number}
                    </span>
                    {message.message_type && (
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getMessageTypeColor(message.message_type)}`}>
                        {getMessageTypeIcon(message.message_type)}
                        <span className="ml-1">{message.message_type}</span>
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {message.content}
                  </p>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs text-gray-400 flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {new Date(message.created_at).toLocaleString()}
                    </span>
                    {!message.processed && (
                      <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Panel derecho - Detalle del mensaje */}
      <div className="flex-1 flex flex-col">
        {selectedMessage ? (
          <>
            {/* Header del mensaje */}
            <div className="p-4 bg-white border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {selectedMessage.sender_name || selectedMessage.phone_number}
                  </h3>
                  <p className="text-sm text-gray-500">{selectedMessage.phone_number}</p>
                </div>
                <div className="flex items-center space-x-2">
                  {!selectedMessage.processed && (
                    <button
                      onClick={() => markAsProcessed(selectedMessage.id)}
                      className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center"
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Marcar como Procesado
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Contenido del mensaje */}
            <div className="flex-1 p-4 bg-gray-50">
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                      <User className="w-6 h-6 text-white" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="bg-green-100 rounded-lg p-3">
                      <p className="text-gray-900">{selectedMessage.content}</p>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {new Date(selectedMessage.created_at).toLocaleString()}
                    </p>
                  </div>
                </div>

                {/* Análisis de IA */}
                {selectedMessage.ai_analysis && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">Análisis de IA:</h4>
                    <p className="text-blue-800 text-sm">{selectedMessage.ai_analysis}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Panel de respuesta */}
            <div className="p-4 bg-white border-t border-gray-200">
              <div className="flex space-x-3">
                <input
                  type="text"
                  placeholder="Escribe tu respuesta..."
                  value={replyText}
                  onChange={(e) => setReplyText(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendReply()}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                />
                <button
                  onClick={sendReply}
                  disabled={sending || !replyText.trim()}
                  className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  <Send className="w-4 h-4 mr-2" />
                  {sending ? 'Enviando...' : 'Enviar'}
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <MessageCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Selecciona un mensaje
              </h3>
              <p className="text-gray-500">
                Elige un mensaje de la lista para ver los detalles y responder
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WhatsAppDashboard;
