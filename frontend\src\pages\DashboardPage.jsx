import React, { useState, useEffect } from 'react';
import AusenciasChart from '../components/AusenciasChart';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

const StatCard = ({ title, value, change, icon, changeColorClass = 'text-green-500' }) => (
  <div className="bg-white dark:bg-slate-800 p-5 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 flex items-center space-x-4">
    <div className="p-3 bg-indigo-100 dark:bg-indigo-700 rounded-lg">
      {/* Reemplazar con el componente de icono real */}
      <span className="text-2xl text-indigo-600 dark:text-indigo-300">{icon || '[ICON]'}</span>
    </div>
    <div>
      <p className="text-sm text-slate-500 dark:text-slate-400">{title}</p>
      <p className="text-2xl font-bold text-slate-700 dark:text-slate-200">{value}</p>
      {change && <p className={`text-xs ${changeColorClass}`}>{change}</p>}
    </div>
  </div>
);

const RecentMessageItem = ({ id, senderType, message, tag, tagColor, time, status, statusColor, onClick }) => (
  <div
    className="flex items-start space-x-3 py-3 border-b border-slate-200 dark:border-slate-700 last:border-b-0 cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors"
    onClick={() => onClick && onClick(id)}
  >
    <div className="p-2 bg-slate-100 dark:bg-slate-700 rounded-md mt-1">
      {/* Icono de mensaje */}
      <span className="text-slate-500 dark:text-slate-400 text-sm">✉️</span>
    </div>
    <div className="flex-1">
      <div className="flex justify-between items-center mb-1">
        <p className="text-sm font-semibold text-slate-700 dark:text-slate-200">{senderType}</p>
        <p className="text-xs text-slate-400 dark:text-slate-500">{time}</p>
      </div>
      <p className="text-sm text-slate-600 dark:text-slate-300 mb-1 line-clamp-2">{message}</p>
      <div className="flex justify-between items-center">
        {tag && <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${tagColor}`}>{tag}</span>}
        {status && <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${statusColor}`}>{status}</span>}
      </div>
    </div>
  </div>
);

const PendingActionItem = ({ id, icon, title, subtitle, time, priority, priorityColor, onComplete }) => (
  <div className="flex items-start space-x-3 py-3 border-b border-slate-200 dark:border-slate-700 last:border-b-0">
    <div className="p-2 bg-slate-100 dark:bg-slate-700 rounded-md mt-1">
      {/* Icono de acción */}
      <span className="text-slate-500 dark:text-slate-400 text-lg">{icon || '⚠️'}</span>
    </div>
    <div className="flex-1">
      <div className="flex justify-between items-center mb-1">
        <p className="text-sm font-semibold text-slate-700 dark:text-slate-200">{title}</p>
        {priority && <span className={`text-xs font-bold px-2 py-0.5 rounded-full ${priorityColor}`}>{priority}</span>}
      </div>
      <p className="text-xs text-slate-500 dark:text-slate-400">{subtitle}</p>
      <p className="text-xs text-slate-400 dark:text-slate-500 mt-0.5">{time}</p>
    </div>
    <button
      className="text-xs text-indigo-600 dark:text-indigo-400 hover:underline whitespace-nowrap"
      onClick={() => onComplete && onComplete(id)}
    >
      Completar
    </button>
  </div>
);


const DashboardPage = () => {
  const [stats, setStats] = useState({
    total_students: 0,
    new_messages: 0,
    absences_today: 0,
    assigned_tasks: 0
  });
  const [ausenciasPorMes, setAusenciasPorMes] = useState([]);
  const [recentMessages, setRecentMessages] = useState([]);
  const [pendingActions, setPendingActions] = useState([]);
  const [loading, setLoading] = useState(true);
  const { token, user } = useAuth();

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Obtener estadísticas
        const statsResponse = await axios.get(`${API_BASE_URL}/api/dashboard/stats`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        setStats(statsResponse.data);

        // Procesar ausencias por mes para el gráfico
        if (statsResponse.data.absences_by_month) {
          const monthNames = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'];
          const chartData = monthNames.map((month, index) => {
            const monthData = statsResponse.data.absences_by_month.find(item => item.month === index + 1);
            return {
              month,
              ausencias: monthData ? monthData.count : 0
            };
          });
          setAusenciasPorMes(chartData);
        }

        // Obtener mensajes recientes
        const messagesResponse = await axios.get(`${API_BASE_URL}/api/dashboard/messages`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        setRecentMessages(messagesResponse.data.slice(0, 5));

        // Generar acciones pendientes basadas en mensajes no procesados
        const pendingMessagesActions = messagesResponse.data
          .filter(msg => !msg.procesado || msg.requires_attention)
          .slice(0, 3)
          .map(msg => ({
            id: msg.id,
            icon: msg.message_type === 'absence' ? '⚠️' : msg.requires_attention ? '🚨' : '📝',
            title: msg.message_type === 'absence' ? 'Responder mensaje de ausencia' :
                   msg.requires_attention ? 'Atención del director requerida' : 'Responder consulta',
            subtitle: `${msg.nombre_remitente || msg.telefono_remitente}: ${msg.texto_mensaje.substring(0, 50)}...`,
            time: `Recibido: ${new Date(msg.fecha_recepcion).toLocaleDateString()}`,
            priority: msg.requires_attention ? 'Alta' : msg.message_type === 'absence' ? 'Media' : 'Baja',
            priorityColor: msg.requires_attention ? 'bg-red-100 text-red-700 dark:bg-red-700 dark:text-red-100' :
                          msg.message_type === 'absence' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-700 dark:text-yellow-100' :
                          'bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100'
          }));

        setPendingActions(pendingMessagesActions);

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (token) {
      fetchDashboardData();
    }
  }, [token]);

  // Funciones para manejar interacciones
  const handleMessageClick = (messageId) => {
    // Navegar al dashboard de WhatsApp con el mensaje seleccionado
    window.location.href = `/whatsapp-dashboard?message=${messageId}`;
  };

  const handleCompleteAction = async (actionId) => {
    try {
      // Marcar mensaje como procesado
      await axios.put(`${API_BASE_URL}/api/dashboard/messages/${actionId}/process`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Actualizar la lista de acciones pendientes
      setPendingActions(prev => prev.filter(action => action.id !== actionId));

      // Actualizar estadísticas
      setStats(prev => ({
        ...prev,
        new_messages: Math.max(0, prev.new_messages - 1)
      }));

    } catch (error) {
      console.error('Error completing action:', error);
    }
  };

  const formatMessageData = (messages) => {
    return messages.map(msg => ({
      id: msg.id,
      senderType: msg.nombre_remitente || 'Usuario',
      message: msg.texto_mensaje,
      tag: msg.message_type === 'absence' ? 'AUSENCIA' :
           msg.message_type === 'question' ? 'CONSULTA' :
           msg.message_type === 'director_attention' ? 'DIRECTOR' : 'GENERAL',
      tagColor: msg.message_type === 'absence' ? 'bg-red-100 text-red-700 dark:bg-red-700 dark:text-red-100' :
                msg.message_type === 'question' ? 'bg-blue-100 text-blue-700 dark:bg-blue-700 dark:text-blue-100' :
                msg.message_type === 'director_attention' ? 'bg-orange-100 text-orange-700 dark:bg-orange-700 dark:text-orange-100' :
                'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-100',
      time: new Date(msg.fecha_recepcion).toLocaleDateString('es-ES', {
        day: '2-digit',
        month: 'short',
        hour: '2-digit',
        minute: '2-digit'
      }),
      status: msg.procesado ? 'Procesado' : 'Pendiente',
      statusColor: msg.procesado ? 'bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100' :
                   'bg-yellow-100 text-yellow-700 dark:bg-yellow-700 dark:text-yellow-100'
    }));
  };

  if (loading) {
    return (
      <div className="p-4 md:p-6 bg-slate-50 dark:bg-slate-900 min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-400">Cargando dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 md:p-6 bg-slate-50 dark:bg-slate-900 min-h-screen">
      <h1 className="text-2xl md:text-3xl font-bold text-slate-800 dark:text-slate-100 mb-2">Panel de Control</h1>
      <p className="text-sm text-slate-600 dark:text-slate-400 mb-6">
        Bienvenido de nuevo, {user?.nombre || 'Usuario'}. Aquí está el resumen de hoy.
      </p>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Estudiantes"
          value={stats.total_students}
          change="Total activos"
          icon="👨‍🎓"
        />
        <StatCard
          title="Mensajes Nuevos"
          value={stats.new_messages}
          change="Sin procesar"
          icon="✉️"
        />
        <StatCard
          title="Ausencias Hoy"
          value={stats.absences_today}
          change="Reportadas hoy"
          icon="🏠"
          changeColorClass="text-red-500"
        />
        <StatCard
          title="Tareas Asignadas"
          value={stats.assigned_tasks}
          change="Pendientes"
          icon="📚"
        />
      </div>

      {/* Ausencias por Mes */}
      <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-slate-700 dark:text-slate-200">Ausencias por Mes</h2>
          <div className="flex items-center space-x-2">
            <button className="p-1 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200">&lt;</button>
            <select className="text-sm bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-200 rounded p-1 border border-slate-300 dark:border-slate-600">
              <option>2025</option>
              <option>2024</option>
            </select>
            <button className="p-1 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200">&gt;</button>
          </div>
        </div>
        <AusenciasChart ausenciasPorMes={ausenciasPorMes} totalAusencias={ausenciasPorMes.reduce((sum, item) => sum + item.ausencias, 0)} />
        <div className="mt-4 flex justify-between items-center text-sm">
          <p className="text-slate-600 dark:text-slate-400">
            🗓️ Total este año: {ausenciasPorMes.reduce((sum, item) => sum + item.ausencias, 0)} ausencias
          </p>
          <a href="/ausencias" className="text-indigo-600 dark:text-indigo-400 hover:underline">Ver reporte completo ↗</a>
        </div>
      </div>

      {/* Mensajes Recientes y Acciones Pendientes */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-slate-700 dark:text-slate-200">Mensajes Recientes</h2>
            <a href="/whatsapp-dashboard" className="text-sm text-indigo-600 dark:text-indigo-400 hover:underline">Ver todos ↗</a>
          </div>
          <div>
            {recentMessages.length > 0 ? (
              formatMessageData(recentMessages).map((msg) => (
                <RecentMessageItem key={msg.id} {...msg} onClick={handleMessageClick} />
              ))
            ) : (
              <div className="text-center py-8 text-slate-500 dark:text-slate-400">
                <p>No hay mensajes recientes</p>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-700 dark:text-slate-200 mb-4">Acciones Pendientes</h2>
          <div>
            {pendingActions.length > 0 ? (
              pendingActions.map((action) => (
                <PendingActionItem key={action.id} {...action} onComplete={handleCompleteAction} />
              ))
            ) : (
              <div className="text-center py-8 text-slate-500 dark:text-slate-400">
                <p>No hay acciones pendientes</p>
                <p className="text-xs mt-1">¡Todo al día! 🎉</p>
              </div>
            )}
          </div>
        </div>
      </div>

    </div>
  );
};

export default DashboardPage;
