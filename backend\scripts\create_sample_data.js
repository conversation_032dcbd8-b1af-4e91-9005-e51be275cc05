const db = require('../config/db');
const bcrypt = require('bcryptjs');

async function createSampleData() {
  try {
    console.log('=== Creando datos de prueba para el dashboard ===');

    // 1. Crear algunos estudiantes
    await createSampleStudents();

    // 2. Crear algunos docentes
    await createSampleTeachers();

    // 3. Crear asignaciones de docentes a grados
    await createTeacherAssignments();

    // 4. <PERSON><PERSON>r mensajes de WhatsApp de prueba
    await createSampleWhatsAppMessages();

    // 5. <PERSON>rear ausencias de prueba
    await createSampleAbsences();

    console.log('\n✅ Datos de prueba creados exitosamente');

  } catch (error) {
    console.error('❌ Error creando datos de prueba:', error);
    throw error;
  }
}

async function createSampleStudents() {
  console.log('\n=== Creando estudiantes de prueba ===');

  const students = [
    { nombre: '<PERSON>', grado: '1er Grado', telefono_padre: '+50312345678', nombre_padre: '<PERSON>' },
    { nombre: '<PERSON>', grado: '2do Grado', telefono_padre: '+50387654321', nombre_padre: 'Elena García' },
    { nombre: 'Pedro Antonio López', grado: '3er Grado', telefono_padre: '+50311223344', nombre_padre: 'Antonio López' },
    { nombre: 'Ana Sofía Martínez', grado: '4to Grado', telefono_padre: '+50355667788', nombre_padre: 'Sofía Martínez' },
    { nombre: 'Luis Fernando Rodríguez', grado: '5to Grado', telefono_padre: '+50399887766', nombre_padre: 'Fernando Rodríguez' },
    { nombre: 'Carmen Isabel Hernández', grado: '6to Grado', telefono_padre: '+50344556677', nombre_padre: 'Isabel Hernández' },
    { nombre: 'Diego Alejandro Morales', grado: '7mo Grado', telefono_padre: '+50322334455', nombre_padre: 'Alejandro Morales' },
    { nombre: 'Valeria Nicole Castro', grado: '8vo Grado', telefono_padre: '+50366778899', nombre_padre: 'Nicole Castro' },
    { nombre: 'Sebastián David Flores', grado: '9no Grado', telefono_padre: '+50377889900', nombre_padre: 'David Flores' },
    { nombre: 'Isabella María Jiménez', grado: 'Kinder 5', telefono_padre: '+50388990011', nombre_padre: 'María Jiménez' }
  ];

  for (const student of students) {
    // Obtener el ID del grado
    const gradoResult = await db.query(
      'SELECT id FROM grados WHERE nombre = $1 AND institucion_id = 1',
      [student.grado]
    );

    if (gradoResult.rows.length > 0) {
      const gradoId = gradoResult.rows[0].id;

      await db.query(`
        INSERT INTO alumnos (
          nombre_completo,
          grado_id,
          telefono_responsable_principal,
          nombre_responsable_principal,
          estado,
          institucion_id
        ) VALUES ($1, $2, $3, $4, 'Activo', 1)
        ON CONFLICT DO NOTHING
      `, [student.nombre, gradoId, student.telefono_padre, student.nombre_padre]);

      console.log(`✅ Estudiante creado: ${student.nombre} - ${student.grado}`);
    }
  }
}

async function createSampleTeachers() {
  console.log('\n=== Creando docentes de prueba ===');

  const teachers = [
    { nombre: 'Prof. Ana Lucía Vásquez', email: '<EMAIL>', telefono: '+50370001111' },
    { nombre: 'Prof. Roberto Carlos Mendoza', email: '<EMAIL>', telefono: '+50370002222' },
    { nombre: 'Prof. Carmen Rosa Delgado', email: '<EMAIL>', telefono: '+50370003333' },
    { nombre: 'Prof. Miguel Ángel Ramos', email: '<EMAIL>', telefono: '+50370004444' },
    { nombre: 'Prof. Silvia Patricia Guerrero', email: '<EMAIL>', telefono: '+50370005555' }
  ];

  const salt = await bcrypt.genSalt(10);
  const defaultPassword = await bcrypt.hash('docente123', salt);

  for (const teacher of teachers) {
    await db.query(`
      INSERT INTO usuarios (
        nombre,
        email,
        password_hash,
        rol,
        telefono,
        institucion_id
      ) VALUES ($1, $2, $3, 'Docente', $4, 1)
      ON CONFLICT (email) DO NOTHING
    `, [teacher.nombre, teacher.email, defaultPassword, teacher.telefono]);

    console.log(`✅ Docente creado: ${teacher.nombre}`);
  }
}

async function createTeacherAssignments() {
  console.log('\n=== Creando asignaciones de docentes ===');

  const assignments = [
    { docente_email: '<EMAIL>', grado: '1er Grado', materia: 'Matemáticas', es_tutor: true },
    { docente_email: '<EMAIL>', grado: '2do Grado', materia: 'Lenguaje', es_tutor: true },
    { docente_email: '<EMAIL>', grado: '3er Grado', materia: 'Ciencias', es_tutor: true },
    { docente_email: '<EMAIL>', grado: '4to Grado', materia: 'Estudios Sociales', es_tutor: true },
    { docente_email: '<EMAIL>', grado: '5to Grado', materia: 'Matemáticas', es_tutor: true }
  ];

  for (const assignment of assignments) {
    // Obtener ID del docente
    const docenteResult = await db.query(
      'SELECT id FROM usuarios WHERE email = $1',
      [assignment.docente_email]
    );

    // Obtener ID del grado
    const gradoResult = await db.query(
      'SELECT id FROM grados WHERE nombre = $1 AND institucion_id = 1',
      [assignment.grado]
    );

    if (docenteResult.rows.length > 0 && gradoResult.rows.length > 0) {
      await db.query(`
        INSERT INTO asignaciones_docente (
          docente_id,
          grado_id,
          materia,
          es_tutor
        ) VALUES ($1, $2, $3, $4)
        ON CONFLICT (docente_id, grado_id, materia) DO NOTHING
      `, [
        docenteResult.rows[0].id,
        gradoResult.rows[0].id,
        assignment.materia,
        assignment.es_tutor
      ]);

      console.log(`✅ Asignación creada: ${assignment.docente_email} → ${assignment.grado}`);
    }
  }
}

async function createSampleWhatsAppMessages() {
  console.log('\n=== Creando mensajes de WhatsApp de prueba ===');

  const messages = [
    {
      telefono: '+50312345678',
      nombre: 'Carlos Pérez',
      mensaje: 'Buenos días, mi hijo Juan Carlos no podrá asistir hoy porque está enfermo',
      tipo: 'absence',
      procesado: false
    },
    {
      telefono: '+50387654321',
      nombre: 'Elena García',
      mensaje: '¿A qué hora es la reunión de padres de familia?',
      tipo: 'question',
      procesado: false
    },
    {
      telefono: '+50311223344',
      nombre: 'Antonio López',
      mensaje: 'Necesito hablar con el director sobre el comportamiento de mi hijo',
      tipo: 'director_attention',
      procesado: false,
      requires_attention: true
    },
    {
      telefono: '+50355667788',
      nombre: 'Sofía Martínez',
      mensaje: 'Ana Sofía tiene cita médica mañana, no asistirá a clases',
      tipo: 'absence',
      procesado: true
    },
    {
      telefono: '+50399887766',
      nombre: 'Fernando Rodríguez',
      mensaje: '¿Cuál es el horario de clases de 5to grado?',
      tipo: 'question',
      procesado: true
    }
  ];

  for (const msg of messages) {
    await db.query(`
      INSERT INTO mensajes_whatsapp (
        telefono_remitente,
        nombre_remitente,
        texto_mensaje,
        message_type,
        procesado,
        requires_attention,
        institucion_id,
        fecha_recepcion
      ) VALUES ($1, $2, $3, $4, $5, $6, 1, NOW() - INTERVAL '${Math.floor(Math.random() * 24)} hours')
    `, [
      msg.telefono,
      msg.nombre,
      msg.mensaje,
      msg.tipo,
      msg.procesado,
      msg.requires_attention || false
    ]);

    console.log(`✅ Mensaje creado: ${msg.nombre} - ${msg.tipo}`);
  }
}

async function createSampleAbsences() {
  console.log('\n=== Creando ausencias de prueba ===');

  const absences = [
    {
      alumno_nombre: 'Juan Carlos Pérez',
      motivo: 'Enfermedad',
      telefono: '+50312345678',
      nombre_reporta: 'Carlos Pérez',
      dias_atras: 0
    },
    {
      alumno_nombre: 'María Elena García',
      motivo: 'Cita médica',
      telefono: '+50387654321',
      nombre_reporta: 'Elena García',
      dias_atras: 1
    },
    {
      alumno_nombre: 'Pedro Antonio López',
      motivo: 'Asuntos familiares',
      telefono: '+50311223344',
      nombre_reporta: 'Antonio López',
      dias_atras: 2
    }
  ];

  for (const absence of absences) {
    // Obtener ID del alumno
    const alumnoResult = await db.query(
      'SELECT id FROM alumnos WHERE nombre_completo = $1',
      [absence.alumno_nombre]
    );

    if (alumnoResult.rows.length > 0) {
      await db.query(`
        INSERT INTO ausencias (
          alumno_id,
          fecha_ausencia,
          motivo,
          justificado,
          reportado_por_telefono,
          reportado_por_nombre,
          notificado_docente
        ) VALUES ($1, CURRENT_DATE - INTERVAL '${absence.dias_atras} days', $2, true, $3, $4, false)
      `, [
        alumnoResult.rows[0].id,
        absence.motivo,
        absence.telefono,
        absence.nombre_reporta
      ]);

      console.log(`✅ Ausencia creada: ${absence.alumno_nombre} - ${absence.motivo}`);
    }
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  createSampleData()
    .then(() => {
      console.log('\n🎉 Datos de prueba creados exitosamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = { createSampleData };
