@echo off
echo ========================================
echo 🔧 SOLUCIONANDO ERROR DE TOKEN JWT
echo ========================================
echo.

echo ⏹️  Deteniendo contenedores...
docker-compose -f docker-compose.dev.yaml down

echo.
echo 🔄 Reiniciando backend con nueva configuracion...
docker-compose -f docker-compose.dev.yaml up backend -d

echo.
echo ⏳ Esperando que el backend este listo...
timeout /t 15 /nobreak > nul

echo.
echo 📊 Verificando logs del backend...
docker-compose -f docker-compose.dev.yaml logs backend --tail=10

echo.
echo ✅ SOLUCION COMPLETADA!
echo.
echo 🌐 Ahora:
echo   1. Ve a http://localhost:5173/clear-storage.html
echo   2. Haz clic en "Limpiar Todo el Storage"
echo   3. Haz clic en "Ir al Login"
echo   4. Inicia sesion como Superadministrador:
echo      Email: <EMAIL>
echo      Password: admin123
echo.
echo 🎯 El error de token deberia estar solucionado!
echo.
pause
