require('dotenv').config();
const { Pool } = require('pg');

// Configuración para conectar a PostgreSQL como superusuario
const pool = new Pool({
  user: process.env.DB_USER || 'ccjap_admin',
  host: process.env.DB_HOST || 'postgres',
  database: 'postgres', // Conectamos a la base de datos por defecto
  password: process.env.DB_PASSWORD || 'K@rur0su24',
  port: process.env.DB_PORT || 5432,
});

async function createN8nDatabase() {
  try {
    const client = await pool.connect();
    
    // Verificar si la base de datos ya existe
    const checkResult = await client.query(
      "SELECT 1 FROM pg_database WHERE datname = 'n8n'"
    );
    
    if (checkResult.rows.length === 0) {
      console.log('La base de datos n8n no existe. Creándola...');
      // Crear la base de datos n8n
      await client.query('CREATE DATABASE n8n');
      console.log('Base de datos n8n creada exitosamente');
    } else {
      console.log('La base de datos n8n ya existe');
    }
    
    client.release();
  } catch (error) {
    console.error('Error al crear la base de datos n8n:', error);
  } finally {
    await pool.end();
  }
}

createN8nDatabase();
