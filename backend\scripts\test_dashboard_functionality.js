const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

// Función para hacer login y obtener token
async function login() {
  try {
    const response = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    return response.data.token;
  } catch (error) {
    console.error('Error en login:', error.response?.data || error.message);
    throw error;
  }
}

// Función para probar endpoints del dashboard
async function testDashboardEndpoints() {
  console.log('🧪 === PRUEBA COMPLETA DE FUNCIONALIDADES DEL DASHBOARD ===\n');

  try {
    // 1. Login
    console.log('🔐 Obteniendo token de autenticación...');
    const token = await login();
    console.log('✅ Login exitoso\n');

    const headers = { Authorization: `Bearer ${token}` };

    // 2. Probar estadísticas del dashboard
    console.log('📊 Probando estadísticas del dashboard...');
    const statsResponse = await axios.get(`${BASE_URL}/api/dashboard/stats`, { headers });
    console.log('✅ Estadísticas obtenidas:');
    console.log(`   - Estudiantes: ${statsResponse.data.total_students}`);
    console.log(`   - Mensajes nuevos: ${statsResponse.data.new_messages}`);
    console.log(`   - Ausencias hoy: ${statsResponse.data.absences_today}`);
    console.log(`   - Tareas asignadas: ${statsResponse.data.assigned_tasks}`);
    console.log('');

    // 3. Probar mensajes del dashboard
    console.log('💬 Probando mensajes del dashboard...');
    const messagesResponse = await axios.get(`${BASE_URL}/api/dashboard/messages`, { headers });
    console.log(`✅ ${messagesResponse.data.length} mensajes obtenidos`);
    if (messagesResponse.data.length > 0) {
      const firstMessage = messagesResponse.data[0];
      console.log(`   - Primer mensaje: ${firstMessage.texto_mensaje?.substring(0, 50)}...`);
      console.log(`   - Remitente: ${firstMessage.nombre_remitente || firstMessage.telefono_remitente}`);
      console.log(`   - Tipo: ${firstMessage.message_type || 'No clasificado'}`);
    }
    console.log('');

    // 4. Probar grupos de WhatsApp
    console.log('👥 Probando grupos de WhatsApp...');
    const gruposResponse = await axios.get(`${BASE_URL}/api/grupos/whatsapp`, { headers });
    console.log(`✅ ${gruposResponse.data.length} grupos obtenidos`);
    gruposResponse.data.forEach((grupo, index) => {
      console.log(`   ${index + 1}. ${grupo.nombre} (${grupo.tipo}) - ${grupo.total_miembros} miembros`);
    });
    console.log('');

    // 5. Probar grados
    console.log('🎓 Probando grados...');
    const gradosResponse = await axios.get(`${BASE_URL}/api/grupos/grados`, { headers });
    console.log(`✅ ${gradosResponse.data.length} grados obtenidos`);
    gradosResponse.data.forEach((grado, index) => {
      console.log(`   ${index + 1}. ${grado.nombre} (${grado.nivel}) - ${grado.alumnos_activos || 0} estudiantes activos`);
    });
    console.log('');

    // 6. Probar asignaciones de docentes
    console.log('👩‍🏫 Probando asignaciones de docentes...');
    const asignacionesResponse = await axios.get(`${BASE_URL}/api/grupos/asignaciones`, { headers });
    console.log(`✅ ${asignacionesResponse.data.length} asignaciones obtenidas`);
    asignacionesResponse.data.forEach((asignacion, index) => {
      console.log(`   ${index + 1}. ${asignacion.docente_nombre} → ${asignacion.grado_nombre} (${asignacion.materia || 'Sin materia'})${asignacion.es_tutor ? ' [TUTOR]' : ''}`);
    });
    console.log('');

    // 7. Probar docentes
    console.log('👨‍🏫 Probando lista de docentes...');
    const docentesResponse = await axios.get(`${BASE_URL}/api/grupos/docentes`, { headers });
    console.log(`✅ ${docentesResponse.data.length} docentes obtenidos`);
    docentesResponse.data.forEach((docente, index) => {
      console.log(`   ${index + 1}. ${docente.nombre} (${docente.rol}) - ${docente.email}`);
    });
    console.log('');

    // 8. Probar ausencias
    console.log('📋 Probando ausencias...');
    const ausenciasResponse = await axios.get(`${BASE_URL}/api/ausencias`, { headers });
    console.log(`✅ ${ausenciasResponse.data.length} ausencias obtenidas`);
    if (ausenciasResponse.data.length > 0) {
      const recentAbsences = ausenciasResponse.data.slice(0, 3);
      recentAbsences.forEach((ausencia, index) => {
        console.log(`   ${index + 1}. ${ausencia.alumno_nombre || 'Alumno no especificado'} - ${ausencia.motivo}`);
      });
    }
    console.log('');

    // 9. Simular envío de mensaje a grupo (solo preparar, no enviar realmente)
    if (gruposResponse.data.length > 0) {
      console.log('📤 Probando preparación de envío de mensaje...');
      const primerGrupo = gruposResponse.data[0];
      console.log(`✅ Grupo seleccionado para prueba: ${primerGrupo.nombre}`);
      console.log('   (Mensaje no enviado - solo prueba de preparación)');
      console.log('');
    }

    // 10. Resumen final
    console.log('📋 === RESUMEN DE FUNCIONALIDADES PROBADAS ===');
    console.log('✅ Autenticación y autorización');
    console.log('✅ Estadísticas del dashboard');
    console.log('✅ Mensajes de WhatsApp');
    console.log('✅ Grupos de WhatsApp');
    console.log('✅ Gestión de grados');
    console.log('✅ Asignaciones de docentes');
    console.log('✅ Lista de docentes');
    console.log('✅ Registro de ausencias');
    console.log('✅ Preparación de mensajes grupales');

    console.log('\n🎉 ¡TODAS LAS FUNCIONALIDADES DEL DASHBOARD ESTÁN OPERATIVAS!');

    // 11. URLs importantes para el usuario
    console.log('\n📋 URLs para acceder al sistema:');
    console.log('   🖥️  Frontend: http://localhost:5173');
    console.log('   🔧 Backend API: http://localhost:3001');
    console.log('   🤖 n8n Editor: http://localhost:5678');
    console.log('   💬 WhatsApp Dashboard: http://localhost:5173/whatsapp-dashboard');
    console.log('   ⚙️  Configuración Avanzada: http://localhost:5173/configuracion-avanzada');

    console.log('\n🔑 Credenciales de prueba:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');

    return true;

  } catch (error) {
    console.error('❌ Error en las pruebas:', error.response?.data || error.message);
    return false;
  }
}

// Función para mostrar datos de ejemplo
async function showSampleData() {
  console.log('\n📊 === DATOS DE EJEMPLO DISPONIBLES ===');
  console.log('👨‍🎓 Estudiantes: 10 estudiantes de ejemplo en diferentes grados');
  console.log('👩‍🏫 Docentes: 5 docentes con asignaciones a grados específicos');
  console.log('💬 Mensajes: 5 mensajes de WhatsApp de diferentes tipos');
  console.log('📋 Ausencias: 3 ausencias reportadas en los últimos días');
  console.log('👥 Grupos: Grupos de WhatsApp por grado y administrativos');
  console.log('🎓 Grados: 12 grados desde Kinder hasta 9no grado');

  console.log('\n🔄 Para regenerar datos de ejemplo, ejecute:');
  console.log('   node scripts/create_sample_data.js');
}

// Ejecutar pruebas
if (require.main === module) {
  testDashboardEndpoints()
    .then((success) => {
      if (success) {
        showSampleData();
      }
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Error ejecutando pruebas:', error);
      process.exit(1);
    });
}

module.exports = { testDashboardEndpoints };
