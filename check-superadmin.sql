-- Script para verificar y crear el Superadministrador
-- Ejecutar en PostgreSQL

-- 1. Verificar si el usuario existe
SELECT 'Usuario encontrado:' as status, id, nombre, email, rol, created_at 
FROM usuarios 
WHERE email = '<EMAIL>';

-- 2. Si no existe, crearlo
INSERT INTO usuarios (nombre, email, password_hash, rol, institucion_id) 
VALUES (
    'Super Administrador', 
    '<EMAIL>', 
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
    'Superadministrador', 
    NULL
) 
ON CONFLICT (email) DO UPDATE SET
    password_hash = '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    rol = 'Superadministrador',
    institucion_id = NULL;

-- 3. Verificar que se creó correctamente
SELECT 'Usuario después de inserción:' as status, id, nombre, email, rol, created_at 
FROM usuarios 
WHERE email = '<EMAIL>';

-- 4. Mostrar todos los usuarios para debug
SELECT 'Todos los usuarios:' as status, id, nombre, email, rol 
FROM usuarios 
ORDER BY created_at;
