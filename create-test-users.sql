-- <PERSON><PERSON>t para crear usuarios de prueba en la base de datos
-- Ejecutar este script en la base de datos para tener usuarios de prueba

-- Limpiar usuarios existentes (opcional)
-- DELETE FROM usuarios WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>');

-- Insertar usuarios de prueba
INSERT INTO usuarios (nombre, email, password, rol, institucion_id, activo, created_at, updated_at) VALUES
-- Superadministrador
('Super Administrador', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Superadministrador', 1, true, NOW(), NOW()),

-- Director
('Director Principal', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Director', 1, true, NOW(), NOW()),

-- Coordinador Académico
('Coordinador Académico', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Coordinador Académico', 1, true, NOW(), NOW()),

-- Docente
('Profesor Juan Pérez', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Docente', 1, true, NOW(), NOW()),

-- Secretario
('Secretaria María López', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Secretario', 1, true, NOW(), NOW());

-- Verificar que los usuarios se crearon correctamente
SELECT id, nombre, email, rol, activo FROM usuarios WHERE email LIKE '%@ccjap.edu.sv';

-- Notas:
-- Todos los usuarios tienen la contraseña: admin123
-- El hash $2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi corresponde a "admin123"
-- 
-- CREDENCIALES DE PRUEBA:
-- 
-- 🔐 SUPERADMINISTRADOR:
--    Email: <EMAIL>
--    Password: admin123
--    Acceso: COMPLETO a todo el sistema
-- 
-- 👨‍💼 DIRECTOR:
--    Email: <EMAIL>
--    Password: admin123
--    Acceso: Gestión de usuarios y académicos (NO configuración avanzada)
-- 
-- 📚 COORDINADOR ACADÉMICO:
--    Email: <EMAIL>
--    Password: admin123
--    Acceso: Gestión académica limitada
-- 
-- 👨‍🏫 DOCENTE:
--    Email: <EMAIL>
--    Password: admin123
--    Acceso: Dashboard docente y reportes
-- 
-- 📝 SECRETARIO:
--    Email: <EMAIL>
--    Password: admin123
--    Acceso: Funciones básicas
