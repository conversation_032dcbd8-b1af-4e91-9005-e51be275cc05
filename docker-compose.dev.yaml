version: '3.8'

services:
  # Base de datos PostgreSQL para desarrollo local
  postgres:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_USER: ccjapuser
      POSTGRES_PASSWORD: ccjappassword
      POSTGRES_DB: ccjapdb
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ccjapuser"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API para desarrollo
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    restart: always
    working_dir: /app
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DATABASE_URL=**************************************************/ccjapdb
      - JWT_SECRET=dev_jwt_secret_2025
      - UPLOAD_DIR=/app/uploads
    volumes:
      - ./backend:/app
      - uploads:/app/uploads
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy

  # Frontend para desarrollo
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    restart: always
    working_dir: /app
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3001
    ports:
      - "5173:5173"
    depends_on:
      - backend

  # n8n para desarrollo
  n8n:
    image: n8nio/n8n
    restart: always
    environment:
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - N8N_ENCRYPTION_KEY=dev_n8n_encrypt_2025
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_USER=ccjapuser
      - DB_POSTGRESDB_PASSWORD=ccjappassword
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=admin123
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=false
    volumes:
      - n8n_data:/home/<USER>/.n8n
    ports:
      - "5678:5678"
    depends_on:
      postgres:
        condition: service_healthy

volumes:
  postgres_data:
  uploads:
  n8n_data:
