const axios = require('axios');

// Configuración
const N8N_BASE_URL = 'http://localhost:5678';
const N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************.YHpG_nNe3ssUgOT7JmYm4a5o8VV221oQZHM3F4bIzzM';

async function testN8nConnection() {
  try {
    console.log('=== Prueba de Conexión con n8n ===');
    console.log(`URL: ${N8N_BASE_URL}`);
    console.log(`API Key: ${N8N_API_KEY.substring(0, 20)}...`);
    
    // 1. Probar conexión básica
    console.log('\n1. Probando conexión básica...');
    const healthResponse = await axios.get(`${N8N_BASE_URL}/healthz`);
    console.log(`✅ Servidor n8n está funcionando (Status: ${healthResponse.status})`);
    
    // 2. Probar API con autenticación
    console.log('\n2. Probando API con autenticación...');
    const apiResponse = await axios.get(`${N8N_BASE_URL}/api/v1/workflows`, {
      headers: {
        'X-N8N-API-KEY': N8N_API_KEY
      }
    });
    
    console.log(`✅ API Key válida (Status: ${apiResponse.status})`);
    console.log(`📊 Workflows encontrados: ${apiResponse.data.data ? apiResponse.data.data.length : 0}`);
    
    // 3. Listar workflows existentes
    if (apiResponse.data.data && apiResponse.data.data.length > 0) {
      console.log('\n3. Workflows existentes:');
      apiResponse.data.data.forEach((workflow, index) => {
        console.log(`   ${index + 1}. ${workflow.name} (ID: ${workflow.id}) - ${workflow.active ? 'Activo' : 'Inactivo'}`);
      });
    } else {
      console.log('\n3. No hay workflows configurados aún');
    }
    
    // 4. Probar endpoint de webhook (si existe)
    console.log('\n4. Probando endpoints de webhook...');
    try {
      const webhookResponse = await axios.post(`${N8N_BASE_URL}/webhook/test`, {
        message: 'Test desde backend',
        timestamp: new Date().toISOString()
      });
      console.log(`✅ Webhook de prueba funcionando (Status: ${webhookResponse.status})`);
    } catch (webhookError) {
      console.log(`⚠️  Webhook de prueba no disponible (esto es normal si no hay workflows con webhook)`);
    }
    
    console.log('\n=== RESUMEN ===');
    console.log('✅ n8n está funcionando correctamente');
    console.log('✅ API Key es válida');
    console.log('✅ Conexión establecida exitosamente');
    console.log('\n📋 Credenciales para usar en la aplicación:');
    console.log(`   URL: ${N8N_BASE_URL}`);
    console.log(`   API Key: ${N8N_API_KEY}`);
    
    return {
      success: true,
      url: N8N_BASE_URL,
      apiKey: N8N_API_KEY,
      workflowCount: apiResponse.data.data ? apiResponse.data.data.length : 0
    };
    
  } catch (error) {
    console.error('\n❌ Error en la conexión con n8n:');
    console.error('Detalles:', error.response?.data || error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Solución: Asegúrate de que n8n esté ejecutándose en http://localhost:5678');
      console.error('   Puedes iniciarlo con: docker-compose up n8n');
    } else if (error.response?.status === 401) {
      console.error('\n💡 Solución: La API Key no es válida. Genera una nueva desde la interfaz de n8n');
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  testN8nConnection()
    .then((result) => {
      if (result.success) {
        console.log('\n🎉 Prueba completada exitosamente');
        process.exit(0);
      } else {
        console.log('\n💥 Prueba falló');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('Error inesperado:', error);
      process.exit(1);
    });
}

module.exports = { testN8nConnection };
