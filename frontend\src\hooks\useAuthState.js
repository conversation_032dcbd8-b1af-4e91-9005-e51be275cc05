// Hook adicional para manejo avanzado del estado de autenticación
import { useState, useEffect, useCallback } from 'react';

/**
 * Hook personalizado para manejo avanzado del estado de autenticación
 * Proporciona funcionalidades adicionales como verificación de permisos,
 * manejo de roles, y validación de sesión
 */
export const useAuthState = () => {
  const [authState, setAuthState] = useState({
    isInitialized: false,
    isAuthenticated: false,
    user: null,
    token: null,
    permissions: [],
    lastActivity: null
  });

  // Función para verificar si el usuario tiene un rol específico
  const hasRole = useCallback((role) => {
    if (!authState.user) return false;
    return authState.user.rol === role;
  }, [authState.user]);

  // Función para verificar si el usuario es superadministrador
  const isSuperAdmin = useCallback(() => {
    if (!authState.user) return false;
    
    const superAdminRoles = [
      'Superadministrador',
      'superadministrador', 
      'SuperAdministrador',
      'SUPERADMINISTRADOR'
    ];
    
    return superAdminRoles.includes(authState.user.rol) || 
           authState.user.email === '<EMAIL>';
  }, [authState.user]);

  // Función para verificar permisos específicos
  const hasPermission = useCallback((permission) => {
    if (isSuperAdmin()) return true;
    return authState.permissions.includes(permission);
  }, [authState.permissions, isSuperAdmin]);

  // Función para verificar si puede acceder a una ruta
  const canAccessRoute = useCallback((route) => {
    if (!authState.isAuthenticated) return false;
    if (isSuperAdmin()) return true;

    const routePermissions = {
      '/configuracion-avanzada': ['admin', 'superadmin'],
      '/usuarios': ['admin', 'director', 'superadmin'],
      '/gestion-academica': ['admin', 'director', 'coordinador', 'superadmin'],
      '/dashboard-docente': ['docente', 'admin', 'superadmin']
    };

    const requiredPermissions = routePermissions[route];
    if (!requiredPermissions) return true;

    return requiredPermissions.some(perm => hasPermission(perm));
  }, [authState.isAuthenticated, hasPermission, isSuperAdmin]);

  // Función para actualizar la actividad del usuario
  const updateActivity = useCallback(() => {
    setAuthState(prev => ({
      ...prev,
      lastActivity: new Date().toISOString()
    }));
  }, []);

  // Función para verificar si la sesión ha expirado
  const isSessionExpired = useCallback(() => {
    if (!authState.lastActivity) return false;
    
    const lastActivity = new Date(authState.lastActivity);
    const now = new Date();
    const diffInHours = (now - lastActivity) / (1000 * 60 * 60);
    
    return diffInHours > 24; // Sesión expira después de 24 horas de inactividad
  }, [authState.lastActivity]);

  // Función para obtener información del usuario formateada
  const getUserInfo = useCallback(() => {
    if (!authState.user) return null;

    return {
      id: authState.user.id,
      nombre: authState.user.nombre,
      email: authState.user.email,
      rol: authState.user.rol,
      iniciales: authState.user.nombre
        .split(' ')
        .map(n => n[0])
        .join('')
        .toUpperCase(),
      isSuperAdmin: isSuperAdmin(),
      permissions: authState.permissions
    };
  }, [authState.user, authState.permissions, isSuperAdmin]);

  // Inicializar el estado desde localStorage
  useEffect(() => {
    const initializeState = () => {
      try {
        const token = localStorage.getItem('token') || localStorage.getItem('authToken');
        const userData = localStorage.getItem('userData');

        if (token && userData) {
          const user = JSON.parse(userData);
          
          // Determinar permisos basados en el rol
          let permissions = [];
          switch (user.rol) {
            case 'Superadministrador':
              permissions = ['admin', 'superadmin', 'director', 'coordinador', 'docente'];
              break;
            case 'Director':
              permissions = ['admin', 'director'];
              break;
            case 'Coordinador Académico':
              permissions = ['coordinador'];
              break;
            case 'Secretario':
              permissions = ['secretario'];
              break;
            case 'Docente':
              permissions = ['docente'];
              break;
            default:
              permissions = [];
          }

          setAuthState({
            isInitialized: true,
            isAuthenticated: true,
            user,
            token,
            permissions,
            lastActivity: new Date().toISOString()
          });
        } else {
          setAuthState({
            isInitialized: true,
            isAuthenticated: false,
            user: null,
            token: null,
            permissions: [],
            lastActivity: null
          });
        }
      } catch (error) {
        console.error('Error initializing auth state:', error);
        setAuthState({
          isInitialized: true,
          isAuthenticated: false,
          user: null,
          token: null,
          permissions: [],
          lastActivity: null
        });
      }
    };

    initializeState();
  }, []);

  return {
    ...authState,
    hasRole,
    isSuperAdmin,
    hasPermission,
    canAccessRoute,
    updateActivity,
    isSessionExpired,
    getUserInfo
  };
};

export default useAuthState;
