const jwt = require('jsonwebtoken');
require('dotenv').config();

// Usar variable de entorno o fallback para desarrollo
const JWT_SECRET = process.env.JWT_SECRET || 'K@rur0su24_JWT_SECRET_2025';

const authMiddleware = (req, res, next) => {
  // Obtener token del header (formato común: Bearer TOKEN)
  const authHeader = req.header('Authorization');

  if (!authHeader) {
    return res.status(401).json({ error: 'No token, autorización denegada.' });
  }

  const tokenParts = authHeader.split(' ');
  if (tokenParts.length !== 2 || tokenParts[0] !== 'Bearer') {
    return res.status(401).json({ error: 'Formato de token inválido. Use Bearer token.' });
  }

  const token = tokenParts[1];

  if (!token) {
    return res.status(401).json({ error: 'No token, autorización denegada.' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded.user; // Añadir payload del usuario (id, rol, etc.) al objeto request
    next();
  } catch (err) {
    console.error('Error de token:', err.message);
    res.status(401).json({ error: 'Token no es válido.' });
  }
};

// Middleware para verificar roles específicos
const authorizeRoles = (...rolesPermitidos) => {
  return (req, res, next) => {
    if (!req.user || !req.user.rol) {
      return res.status(403).json({ error: 'Acceso denegado. Rol de usuario no encontrado en token.' });
    }

    // Superadministrador tiene acceso a todo sin restricciones
    if (req.user.rol === 'Superadministrador') {
      console.log('Superadministrador accediendo a:', req.originalUrl);
      return next();
    }

    // Para otros roles, verificar si están en la lista de roles permitidos
    if (!rolesPermitidos.includes(req.user.rol)) {
      console.log(`Acceso denegado para rol '${req.user.rol}' en ruta '${req.originalUrl}'. Roles permitidos:`, rolesPermitidos);
      return res.status(403).json({ error: `Acceso denegado. Rol '${req.user.rol}' no tiene permiso para este recurso.` });
    }

    console.log(`Acceso permitido para rol '${req.user.rol}' en ruta '${req.originalUrl}'`);
    next();
  };
};

// Middleware especial para rutas que requieren institución pero el Superadministrador puede acceder sin ella
const requireInstitutionOrSuperAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ error: 'Usuario no autenticado.' });
  }

  // Superadministrador puede acceder sin institución
  if (req.user.rol === 'Superadministrador') {
    console.log('Superadministrador accediendo sin restricción de institución a:', req.originalUrl);
    return next();
  }

  // Otros usuarios necesitan tener institución
  if (!req.user.institucion_id) {
    return res.status(403).json({ error: 'Usuario debe estar asociado a una institución.' });
  }

  next();
};

module.exports = {
  authMiddleware,
  authorizeRoles,
  requireInstitutionOrSuperAdmin
};
