import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { 
  Users, 
  GraduationCap, 
  MessageSquare, 
  BarChart3, 
  Settings, 
  School,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Clock,
  Bell,
  Shield
} from 'lucide-react';
import { useGSAP } from '../utils/animations';

const ConfiguracionDirector = () => {
  const [configuracion, setConfiguracion] = useState({
    institucion: {
      nombre: '',
      direccion: '',
      telefono: '',
      email: '',
      sitio_web: '',
      logo_url: ''
    },
    notificaciones: {
      ausencias_email: true,
      mensajes_whatsapp: true,
      reportes_semanales: true,
      alertas_sistema: true
    },
    horarios: {
      inicio_clases: '07:00',
      fin_clases: '15:00',
      recreo_inicio: '10:00',
      recreo_fin: '10:30'
    },
    limites: {
      max_ausencias_mes: 5,
      max_estudiantes_grado: 35,
      dias_reporte_ausencias: 3
    }
  });

  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('institucion');
  
  const containerRef = useRef(null);
  const { fadeInUp, staggerIn, scaleIn } = useGSAP();

  useEffect(() => {
    if (containerRef.current) {
      fadeInUp(containerRef.current, { delay: 0.2 });
    }
    
    // Animar las tarjetas
    setTimeout(() => {
      staggerIn('.config-card', { stagger: 0.1 });
    }, 300);
  }, [fadeInUp, staggerIn]);

  const cargarConfiguracion = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/config/director', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setConfiguracion(prev => ({ ...prev, ...data }));
      }
    } catch (error) {
      console.error('Error cargando configuración:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const guardarConfiguracion = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/config/director', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(configuracion)
      });
      
      if (response.ok) {
        alert('Configuración guardada exitosamente');
      } else {
        throw new Error('Error al guardar configuración');
      }
    } catch (error) {
      console.error('Error guardando configuración:', error);
      alert('Error al guardar la configuración');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    cargarConfiguracion();
  }, []);

  const tabs = [
    { id: 'institucion', label: 'Institución', icon: School },
    { id: 'notificaciones', label: 'Notificaciones', icon: Bell },
    { id: 'horarios', label: 'Horarios', icon: Clock },
    { id: 'limites', label: 'Límites', icon: Shield }
  ];

  const estadisticas = [
    { 
      titulo: 'Total Estudiantes', 
      valor: '245', 
      icono: GraduationCap, 
      color: 'bg-blue-500',
      descripcion: 'Estudiantes activos'
    },
    { 
      titulo: 'Docentes', 
      valor: '18', 
      icono: Users, 
      color: 'bg-green-500',
      descripcion: 'Personal docente'
    },
    { 
      titulo: 'Mensajes Hoy', 
      valor: '12', 
      icono: MessageSquare, 
      color: 'bg-purple-500',
      descripcion: 'WhatsApp recibidos'
    },
    { 
      titulo: 'Ausencias', 
      valor: '3', 
      icono: BarChart3, 
      color: 'bg-orange-500',
      descripcion: 'Reportadas hoy'
    }
  ];

  const handleInputChange = (seccion, campo, valor) => {
    setConfiguracion(prev => ({
      ...prev,
      [seccion]: {
        ...prev[seccion],
        [campo]: valor
      }
    }));
  };

  const renderInstitucionTab = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <School className="inline h-4 w-4 mr-2" />
            Nombre de la Institución
          </label>
          <Input
            value={configuracion.institucion.nombre}
            onChange={(e) => handleInputChange('institucion', 'nombre', e.target.value)}
            placeholder="Nombre del colegio"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Mail className="inline h-4 w-4 mr-2" />
            Email Institucional
          </label>
          <Input
            type="email"
            value={configuracion.institucion.email}
            onChange={(e) => handleInputChange('institucion', 'email', e.target.value)}
            placeholder="<EMAIL>"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Phone className="inline h-4 w-4 mr-2" />
            Teléfono
          </label>
          <Input
            value={configuracion.institucion.telefono}
            onChange={(e) => handleInputChange('institucion', 'telefono', e.target.value)}
            placeholder="+503 2XXX-XXXX"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <MapPin className="inline h-4 w-4 mr-2" />
            Dirección
          </label>
          <Input
            value={configuracion.institucion.direccion}
            onChange={(e) => handleInputChange('institucion', 'direccion', e.target.value)}
            placeholder="Dirección completa"
          />
        </div>
      </div>
    </div>
  );

  const renderNotificacionesTab = () => (
    <div className="space-y-4">
      {Object.entries(configuracion.notificaciones).map(([key, value]) => (
        <div key={key} className="flex items-center justify-between p-3 border rounded-lg">
          <div>
            <h4 className="font-medium capitalize">
              {key.replace('_', ' ')}
            </h4>
            <p className="text-sm text-gray-500">
              {key === 'ausencias_email' && 'Recibir notificaciones por email de ausencias'}
              {key === 'mensajes_whatsapp' && 'Notificaciones de mensajes de WhatsApp'}
              {key === 'reportes_semanales' && 'Reportes automáticos semanales'}
              {key === 'alertas_sistema' && 'Alertas importantes del sistema'}
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => handleInputChange('notificaciones', key, e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
      ))}
    </div>
  );

  const renderHorariosTab = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.entries(configuracion.horarios).map(([key, value]) => (
          <div key={key}>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Clock className="inline h-4 w-4 mr-2" />
              {key.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </label>
            <Input
              type="time"
              value={value}
              onChange={(e) => handleInputChange('horarios', key, e.target.value)}
            />
          </div>
        ))}
      </div>
    </div>
  );

  const renderLimitesTab = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.entries(configuracion.limites).map(([key, value]) => (
          <div key={key}>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Shield className="inline h-4 w-4 mr-2" />
              {key.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </label>
            <Input
              type="number"
              value={value}
              onChange={(e) => handleInputChange('limites', key, parseInt(e.target.value))}
              min="1"
            />
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="container mx-auto py-8 px-4" ref={containerRef}>
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Panel de Administración</h1>
          <p className="text-gray-600 mt-2">
            Configuración y gestión administrativa de la institución
          </p>
        </div>

        {/* Estadísticas */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {estadisticas.map((stat, index) => (
            <Card key={index} className="config-card">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className={`${stat.color} p-3 rounded-lg`}>
                    <stat.icono className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-4">
                    <p className="text-2xl font-bold text-gray-900">{stat.valor}</p>
                    <p className="text-sm text-gray-600">{stat.descripcion}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Configuración */}
        <Card className="config-card">
          <CardHeader>
            <CardTitle>Configuración Administrativa</CardTitle>
            <CardDescription>
              Gestiona la configuración general de la institución
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Tabs */}
            <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <tab.icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div className="min-h-[300px]">
              {activeTab === 'institucion' && renderInstitucionTab()}
              {activeTab === 'notificaciones' && renderNotificacionesTab()}
              {activeTab === 'horarios' && renderHorariosTab()}
              {activeTab === 'limites' && renderLimitesTab()}
            </div>

            {/* Botones de acción */}
            <div className="flex justify-end space-x-3 mt-6 pt-6 border-t">
              <Button
                variant="outline"
                onClick={cargarConfiguracion}
                disabled={isLoading}
              >
                Recargar
              </Button>
              <Button
                onClick={guardarConfiguracion}
                disabled={isLoading}
                className="min-w-[120px]"
              >
                {isLoading ? 'Guardando...' : 'Guardar Cambios'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ConfiguracionDirector;
