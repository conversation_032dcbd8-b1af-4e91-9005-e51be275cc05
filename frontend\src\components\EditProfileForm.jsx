import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../context/AuthContext';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Loader2, User, Upload, X, Shuffle, Palette, Info, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { generateAvatarDataUrl, generateMultipleAvatars } from '../utils/avatarGenerator';
import { useGSAP } from '../utils/animations';
import { useNotification, NotificationContainer } from './ui/notification';

const EditProfileForm = () => {
  const { user, updateUser } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [showAvatarSelector, setShowAvatarSelector] = useState(false);
  const [generatedAvatars, setGeneratedAvatars] = useState([]);
  const [formData, setFormData] = useState({
    nombre: '',
    email: '',
    foto_perfil_url: ''
  });
  const [originalEmail, setOriginalEmail] = useState('');
  const [emailValidation, setEmailValidation] = useState({
    isValid: true,
    message: '',
    isChecking: false
  });

  const formRef = useRef(null);
  const avatarRef = useRef(null);
  const { fadeInUp, scaleIn, buttonHover, staggerIn } = useGSAP();
  const { notifications, removeNotification, showSuccess, showError } = useNotification();

  // Cargar datos del usuario al montar el componente
  useEffect(() => {
    if (user) {
      setFormData({
        nombre: user.nombre || '',
        email: user.email || '',
        foto_perfil_url: user.foto_perfil_url || ''
      });

      // Guardar el email original para comparación
      setOriginalEmail(user.email || '');

      if (user.foto_perfil_url) {
        setPreviewImage(user.foto_perfil_url);
      } else {
        // Generar avatar por defecto si no tiene foto
        const defaultAvatar = generateAvatarDataUrl(user.nombre || user.email);
        setPreviewImage(defaultAvatar);
      }
    }
  }, [user]);

  // Animaciones al montar el componente
  useEffect(() => {
    if (formRef.current) {
      fadeInUp(formRef.current, { delay: 0.2 });
    }
  }, [fadeInUp]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Validación en tiempo real para el email
    if (name === 'email') {
      validateEmail(value);
    }
  };

  // Función para validar email en tiempo real
  const validateEmail = async (email) => {
    // Resetear validación
    setEmailValidation({
      isValid: true,
      message: '',
      isChecking: false
    });

    // Si está vacío, no validar
    if (!email.trim()) {
      return;
    }

    // Validar formato
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setEmailValidation({
        isValid: false,
        message: 'Formato de email inválido',
        isChecking: false
      });
      return;
    }

    // Si es el mismo email original, no verificar disponibilidad
    if (email === originalEmail) {
      setEmailValidation({
        isValid: true,
        message: 'Email actual',
        isChecking: false
      });
      return;
    }

    // Verificar disponibilidad del email
    setEmailValidation({
      isValid: true,
      message: '',
      isChecking: true
    });

    try {
      const response = await fetch(`/api/users/check-email?email=${encodeURIComponent(email)}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (response.ok) {
        if (data.available) {
          setEmailValidation({
            isValid: true,
            message: '✅ Email disponible',
            isChecking: false
          });
        } else {
          setEmailValidation({
            isValid: false,
            message: '❌ Este email ya está en uso',
            isChecking: false
          });
        }
      }
    } catch (error) {
      console.error('Error verificando email:', error);
      setEmailValidation({
        isValid: true,
        message: '',
        isChecking: false
      });
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validar tipo de archivo
    if (!file.type.startsWith('image/')) {
      alert('Por favor, sube un archivo de imagen válido (JPEG, PNG, etc.)');
      return;
    }

    // Validar tamaño del archivo (máximo 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('La imagen es demasiado grande. El tamaño máximo permitido es 5MB.');
      return;
    }

    // Crear vista previa de la imagen
    const reader = new FileReader();
    reader.onloadend = () => {
      setPreviewImage(reader.result);
      setFormData(prev => ({
        ...prev,
        foto_perfil_url: reader.result
      }));
    };
    reader.readAsDataURL(file);
  };

  const removeImage = () => {
    setPreviewImage('');
    setFormData(prev => ({
      ...prev,
      foto_perfil_url: ''
    }));
  };

  // Generar avatares aleatorios
  const generateRandomAvatars = () => {
    const seed = formData.nombre || formData.email || 'user';
    const avatars = generateMultipleAvatars(seed, 6);
    setGeneratedAvatars(avatars);
    setShowAvatarSelector(true);

    // Animar la aparición de los avatares
    setTimeout(() => {
      staggerIn('.avatar-option', { stagger: 0.1 });
    }, 100);
  };

  // Seleccionar avatar generado
  const selectGeneratedAvatar = (avatar) => {
    setPreviewImage(avatar.dataUrl);
    setShowAvatarSelector(false);

    // Animar la selección
    if (avatarRef.current) {
      scaleIn(avatarRef.current);
    }
  };

  // Generar un avatar completamente aleatorio
  const generateSingleRandomAvatar = () => {
    const seed = `${formData.nombre || formData.email || 'user'}-${Date.now()}`;
    const newAvatar = generateAvatarDataUrl(seed);
    setPreviewImage(newAvatar);

    // Animar el cambio
    if (avatarRef.current) {
      scaleIn(avatarRef.current);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validar el formulario
    if (!formData.nombre.trim()) {
      showError('Error', 'El nombre es obligatorio');
      return;
    }

    if (!formData.email.trim()) {
      showError('Error', 'El correo electrónico es obligatorio');
      return;
    }

    // Validar formato de email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      showError('Error', 'Por favor ingresa un correo electrónico válido');
      return;
    }

    // Verificar si el email es válido según nuestra validación
    if (!emailValidation.isValid && formData.email !== originalEmail) {
      showError('Error', 'El correo electrónico no es válido o ya está en uso');
      return;
    }

    // Confirmar cambio de email si es diferente
    if (formData.email !== originalEmail) {
      const confirmChange = window.confirm(
        `¿Estás seguro de que quieres cambiar tu email de "${originalEmail}" a "${formData.email}"?\n\n` +
        'Tendrás que usar el nuevo email para iniciar sesión en el futuro.'
      );

      if (!confirmChange) {
        return;
      }
    }

    setIsLoading(true);
    setIsSubmitting(true);

    try {
      // Crear FormData para manejar la carga de archivos
      const formDataToSend = new FormData();

      // Agregar campos al FormData
      formDataToSend.append('nombre', formData.nombre);
      formDataToSend.append('email', formData.email);

      // Si hay una nueva imagen, agregarla al FormData
      if (previewImage && previewImage !== user.foto_perfil_url) {
        // Si previewImage es una URL de datos (imagen subida o avatar generado)
        if (previewImage.startsWith('data:image')) {
          try {
            // Convertir la URL de datos a un blob
            const blob = await fetch(previewImage).then(res => res.blob());
            formDataToSend.append('foto_perfil', blob, 'profile.jpg');
          } catch (error) {
            console.error('Error al procesar la imagen:', error);
            // Si hay error, enviar la URL directamente
            formDataToSend.append('foto_perfil_url', previewImage);
          }
        }
      } else if (!previewImage && user.foto_perfil_url) {
        // Si se eliminó la imagen
        formDataToSend.append('removeImage', 'true');
      }

      const response = await fetch('/api/users/me', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formDataToSend
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Error al actualizar el perfil');
      }

      // Actualizar el contexto de autenticación
      await updateUser(data);

      // Mensaje específico si se cambió el email
      if (formData.email !== originalEmail) {
        showSuccess(
          '¡Email actualizado!',
          `Tu email ha sido cambiado exitosamente a "${formData.email}". Usa este email para iniciar sesión en el futuro.`
        );
        // Actualizar el email original para futuras comparaciones
        setOriginalEmail(formData.email);
      } else {
        showSuccess('¡Éxito!', 'Perfil actualizado correctamente');
      }
    } catch (error) {
      console.error('Error al actualizar el perfil:', error);

      // Manejar diferentes tipos de errores
      let errorMessage = 'Error al actualizar el perfil';

      if (error.message.includes('NetworkError')) {
        errorMessage = 'Error de conexión. Por favor, verifica tu conexión a internet.';
      } else if (error.message.includes('email_unique')) {
        errorMessage = 'Este correo electrónico ya está en uso. Por favor, utiliza otro.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      showError('Error', errorMessage);
    } finally {
      setIsLoading(false);
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <NotificationContainer
        notifications={notifications}
        onRemove={removeNotification}
      />
      <Card className="w-full max-w-2xl mx-auto" ref={formRef}>
      <CardHeader>
        <CardTitle>Editar perfil</CardTitle>
        <CardDescription>
          Actualiza tu información personal y tu foto de perfil.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label htmlFor="foto_perfil" className="block text-sm font-medium text-gray-700">Foto de perfil</label>
            <div className="flex items-center space-x-4">
              <div className="relative" ref={avatarRef}>
                {previewImage ? (
                  <div className="relative">
                    <img
                      src={previewImage}
                      alt="Vista previa"
                      className="h-24 w-24 rounded-full object-cover border-2 border-gray-200 shadow-lg"
                    />
                    <button
                      type="button"
                      onClick={removeImage}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-all duration-200 hover:scale-110"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ) : (
                  <div className="h-24 w-24 rounded-full bg-gray-200 flex items-center justify-center">
                    <User className="h-12 w-12 text-gray-400" />
                  </div>
                )}
              </div>
              <div className="flex flex-col space-y-2">
                <div className="flex space-x-2">
                  <label
                    htmlFor="foto_perfil"
                    className="cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:scale-105"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    {previewImage ? 'Cambiar' : 'Subir'}
                  </label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={generateSingleRandomAvatar}
                    className="inline-flex items-center transition-all duration-200 hover:scale-105"
                  >
                    <Shuffle className="h-4 w-4 mr-2" />
                    Aleatorio
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={generateRandomAvatars}
                    className="inline-flex items-center transition-all duration-200 hover:scale-105"
                  >
                    <Palette className="h-4 w-4 mr-2" />
                    Elegir
                  </Button>
                </div>
                <Input
                  id="foto_perfil"
                  name="foto_perfil"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageChange}
                />
                <p className="mt-1 text-xs text-gray-500">
                  Sube una imagen, genera un avatar aleatorio o elige entre varias opciones
                </p>
              </div>
            </div>
          </div>

          {/* Selector de avatares */}
          {showAvatarSelector && (
            <div className="space-y-4 p-4 bg-gray-50 rounded-lg border">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Elige tu avatar</h3>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAvatarSelector(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <div className="grid grid-cols-3 gap-4">
                {generatedAvatars.map((avatar) => (
                  <div
                    key={avatar.id}
                    className="avatar-option cursor-pointer p-2 rounded-lg border-2 border-transparent hover:border-indigo-500 transition-all duration-200 hover:scale-105"
                    onClick={() => selectGeneratedAvatar(avatar)}
                  >
                    <img
                      src={avatar.dataUrl}
                      alt={`Avatar ${avatar.id + 1}`}
                      className="w-full h-20 rounded-full object-cover mx-auto"
                    />
                    <p className="text-xs text-center mt-2 text-gray-600 capitalize">
                      {avatar.style}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="space-y-2">
            <label htmlFor="nombre" className="block text-sm font-medium text-gray-700">Nombre completo</label>
            <Input
              id="nombre"
              name="nombre"
              type="text"
              value={formData.nombre}
              onChange={handleChange}
              required
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Correo electrónico
              {formData.email !== originalEmail && (
                <span className="ml-2 text-xs text-amber-600 font-normal">
                  (Cambio pendiente)
                </span>
              )}
            </label>
            <div className="relative">
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                required
                className={`pr-10 ${
                  emailValidation.isChecking
                    ? 'border-blue-300'
                    : !emailValidation.isValid && formData.email !== originalEmail
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                    : emailValidation.isValid && formData.email !== originalEmail && emailValidation.message.includes('disponible')
                    ? 'border-green-300 focus:border-green-500 focus:ring-green-500'
                    : ''
                }`}
                placeholder="<EMAIL>"
              />

              {/* Indicador de estado */}
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                {emailValidation.isChecking ? (
                  <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                ) : formData.email !== originalEmail ? (
                  emailValidation.isValid ? (
                    emailValidation.message.includes('disponible') && (
                      <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                    )
                  ) : (
                    <div className="h-2 w-2 bg-red-500 rounded-full"></div>
                  )
                ) : null}
              </div>
            </div>

            {/* Mensaje de validación */}
            {emailValidation.message && formData.email !== originalEmail && (
              <p className={`text-xs ${
                emailValidation.isValid
                  ? 'text-green-600'
                  : 'text-red-600'
              }`}>
                {emailValidation.message}
              </p>
            )}

            {/* Información adicional */}
            {formData.email === originalEmail ? (
              <p className="text-xs text-gray-500">
                Este es tu email actual para iniciar sesión
              </p>
            ) : (
              <p className="text-xs text-amber-600">
                ⚠️ Cambiar el email afectará tu forma de iniciar sesión
              </p>
            )}
          </div>

          {/* Información sobre cambio de email */}
          {formData.email !== originalEmail && (
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-amber-800 mb-2">
                    Importante: Cambio de Email
                  </h4>
                  <ul className="text-sm text-amber-700 space-y-1">
                    <li>• Tu email actual: <strong>{originalEmail}</strong></li>
                    <li>• Nuevo email: <strong>{formData.email}</strong></li>
                    <li>• Deberás usar el nuevo email para iniciar sesión</li>
                    <li>• Todas las notificaciones se enviarán al nuevo email</li>
                    <li>• Este cambio es permanente</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                // Restablecer el formulario a los valores originales
                if (user) {
                  setFormData({
                    nombre: user.nombre || '',
                    email: user.email || '',
                    foto_perfil_url: user.foto_perfil_url || ''
                  });
                  setPreviewImage(user.foto_perfil_url || '');
                }
              }}
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Guardando...
                </>
              ) : 'Guardar cambios'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
    </>
  );
};

export default EditProfileForm;
