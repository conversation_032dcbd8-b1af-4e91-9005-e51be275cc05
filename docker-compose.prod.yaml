version: '3.8'

services:
  # Base de datos PostgreSQL para producción
  postgres:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_USER: ${DB_USER:-ccjap_admin}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-K@rur0su24}
      POSTGRES_DB: ${DB_NAME:-ccjap_db}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-postgres.sh:/docker-entrypoint-initdb.d/init-postgres.sh
    networks:
      - dokploy-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-ccjap_admin}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API para producción
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    restart: always
    working_dir: /usr/src/app
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DATABASE_URL=postgresql://${DB_USER:-ccjap_admin}:${DB_PASSWORD:-K@rur0su24}@postgres:5432/${DB_NAME:-ccjap_db}
      - JWT_SECRET=${JWT_SECRET:-K@rur0su24_JWT_SECRET_2025}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-24h}
      - UPLOAD_DIR=/usr/src/app/uploads
      - WHATSAPP_API_KEY=${WHATSAPP_API_KEY}
      - WHATSAPP_PHONE_NUMBER=${WHATSAPP_PHONE_NUMBER}
      - WHATSAPP_WEBHOOK_URL=${WHATSAPP_WEBHOOK_URL}
      - N8N_URL=http://n8n:5678
      - N8N_API_KEY=${N8N_API_KEY}
      - APP_URL=${APP_URL:-https://ccjap.echolab.xyz}
      - ENABLE_WHATSAPP_NOTIFICATIONS=${ENABLE_WHATSAPP_NOTIFICATIONS:-true}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - uploads:/usr/src/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - dokploy-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ccjap-backend.rule=Host(`${BACKEND_DOMAIN:-api.ccjap.echolab.xyz}`)"
      - "traefik.http.routers.ccjap-backend.entrypoints=websecure"
      - "traefik.http.routers.ccjap-backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.ccjap-backend.loadbalancer.server.port=3001"
      # CORS headers
      - "traefik.http.middlewares.ccjap-cors.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE,PATCH"
      - "traefik.http.middlewares.ccjap-cors.headers.accesscontrolallowheaders=*"
      - "traefik.http.middlewares.ccjap-cors.headers.accesscontrolalloworiginlist=https://${FRONTEND_DOMAIN:-ccjap.echolab.xyz}"
      - "traefik.http.middlewares.ccjap-cors.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.ccjap-cors.headers.addvaryheader=true"
      - "traefik.http.routers.ccjap-backend.middlewares=ccjap-cors"

  # Frontend para producción
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    restart: always
    environment:
      - NODE_ENV=production
      - VITE_API_URL=https://${BACKEND_DOMAIN:-api.ccjap.echolab.xyz}
    networks:
      - dokploy-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ccjap-frontend.rule=Host(`${FRONTEND_DOMAIN:-ccjap.echolab.xyz}`)"
      - "traefik.http.routers.ccjap-frontend.entrypoints=websecure"
      - "traefik.http.routers.ccjap-frontend.tls.certresolver=letsencrypt"
      - "traefik.http.services.ccjap-frontend.loadbalancer.server.port=80"

  # n8n para producción
  n8n:
    image: n8nio/n8n
    restart: always
    environment:
      - N8N_HOST=${N8N_DOMAIN:-n8n.echolab.xyz}
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - N8N_HOST_WHITELIST=${N8N_DOMAIN:-n8n.echolab.xyz}
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY:-K@rur0su24_N8N_ENCRYPT_2025}
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_DATABASE=${DB_NAME:-ccjap_db}
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_USER=${DB_USER:-ccjap_admin}
      - DB_POSTGRESDB_PASSWORD=${DB_PASSWORD:-K@rur0su24}
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_USER:-admin}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_PASSWORD:-K@rur0su24}
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=false
      - WEBHOOK_URL=https://${N8N_DOMAIN:-n8n.echolab.xyz}
    volumes:
      - n8n_data:/home/<USER>/.n8n
    networks:
      - dokploy-network
    depends_on:
      postgres:
        condition: service_healthy
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ccjap-n8n.rule=Host(`${N8N_DOMAIN:-n8n.echolab.xyz}`)"
      - "traefik.http.routers.ccjap-n8n.entrypoints=websecure"
      - "traefik.http.routers.ccjap-n8n.tls.certresolver=letsencrypt"
      - "traefik.http.services.ccjap-n8n.loadbalancer.server.port=5678"

networks:
  dokploy-network:
    external: true

volumes:
  postgres_data:
  uploads:
  n8n_data:
