import { gsap } from 'gsap';

/**
 * Configuraciones de animaciones predefinidas
 */
export const animationConfig = {
  // Duraciones estándar
  duration: {
    fast: 0.3,
    normal: 0.5,
    slow: 0.8,
    verySlow: 1.2
  },
  
  // Easing predefinidos
  easing: {
    smooth: "power2.out",
    bounce: "back.out(1.7)",
    elastic: "elastic.out(1, 0.3)",
    sharp: "power3.inOut"
  }
};

/**
 * Animación de entrada para elementos
 * @param {HTMLElement|string} element - Elemento o selector
 * @param {Object} options - Opciones de animación
 */
export const fadeInUp = (element, options = {}) => {
  const defaults = {
    duration: animationConfig.duration.normal,
    ease: animationConfig.easing.smooth,
    y: 30,
    opacity: 0,
    delay: 0
  };
  
  const config = { ...defaults, ...options };
  
  gsap.fromTo(element, 
    { 
      opacity: 0, 
      y: config.y 
    },
    {
      opacity: 1,
      y: 0,
      duration: config.duration,
      ease: config.ease,
      delay: config.delay
    }
  );
};

/**
 * Animación de entrada lateral
 * @param {HTMLElement|string} element - Elemento o selector
 * @param {Object} options - Opciones de animación
 */
export const slideInLeft = (element, options = {}) => {
  const defaults = {
    duration: animationConfig.duration.normal,
    ease: animationConfig.easing.smooth,
    x: -50,
    delay: 0
  };
  
  const config = { ...defaults, ...options };
  
  gsap.fromTo(element,
    {
      opacity: 0,
      x: config.x
    },
    {
      opacity: 1,
      x: 0,
      duration: config.duration,
      ease: config.ease,
      delay: config.delay
    }
  );
};

/**
 * Animación de escala con bounce
 * @param {HTMLElement|string} element - Elemento o selector
 * @param {Object} options - Opciones de animación
 */
export const scaleIn = (element, options = {}) => {
  const defaults = {
    duration: animationConfig.duration.normal,
    ease: animationConfig.easing.bounce,
    scale: 0.8,
    delay: 0
  };
  
  const config = { ...defaults, ...options };
  
  gsap.fromTo(element,
    {
      opacity: 0,
      scale: config.scale
    },
    {
      opacity: 1,
      scale: 1,
      duration: config.duration,
      ease: config.ease,
      delay: config.delay
    }
  );
};

/**
 * Animación de hover para botones
 * @param {HTMLElement|string} element - Elemento o selector
 */
export const buttonHover = (element) => {
  const el = typeof element === 'string' ? document.querySelector(element) : element;
  
  if (!el) return;
  
  el.addEventListener('mouseenter', () => {
    gsap.to(el, {
      scale: 1.05,
      duration: animationConfig.duration.fast,
      ease: animationConfig.easing.smooth
    });
  });
  
  el.addEventListener('mouseleave', () => {
    gsap.to(el, {
      scale: 1,
      duration: animationConfig.duration.fast,
      ease: animationConfig.easing.smooth
    });
  });
};

/**
 * Animación de carga para elementos
 * @param {HTMLElement|string} element - Elemento o selector
 */
export const loadingPulse = (element) => {
  gsap.to(element, {
    opacity: 0.5,
    duration: 0.8,
    ease: "power2.inOut",
    yoyo: true,
    repeat: -1
  });
};

/**
 * Detener animación de carga
 * @param {HTMLElement|string} element - Elemento o selector
 */
export const stopLoadingPulse = (element) => {
  gsap.killTweensOf(element);
  gsap.to(element, {
    opacity: 1,
    duration: animationConfig.duration.fast
  });
};

/**
 * Animación de lista escalonada
 * @param {HTMLElement[]|string} elements - Elementos o selector
 * @param {Object} options - Opciones de animación
 */
export const staggerIn = (elements, options = {}) => {
  const defaults = {
    duration: animationConfig.duration.normal,
    ease: animationConfig.easing.smooth,
    stagger: 0.1,
    y: 20
  };
  
  const config = { ...defaults, ...options };
  
  gsap.fromTo(elements,
    {
      opacity: 0,
      y: config.y
    },
    {
      opacity: 1,
      y: 0,
      duration: config.duration,
      ease: config.ease,
      stagger: config.stagger
    }
  );
};

/**
 * Animación de notificación
 * @param {HTMLElement|string} element - Elemento o selector
 * @param {string} type - Tipo de notificación (success, error, warning, info)
 */
export const notificationSlide = (element, type = 'info') => {
  const colors = {
    success: '#10B981',
    error: '#EF4444',
    warning: '#F59E0B',
    info: '#3B82F6'
  };
  
  gsap.fromTo(element,
    {
      x: 300,
      opacity: 0
    },
    {
      x: 0,
      opacity: 1,
      duration: animationConfig.duration.normal,
      ease: animationConfig.easing.bounce
    }
  );
  
  // Auto-hide después de 3 segundos
  gsap.to(element, {
    x: 300,
    opacity: 0,
    duration: animationConfig.duration.normal,
    ease: animationConfig.easing.smooth,
    delay: 3
  });
};

/**
 * Animación de modal
 * @param {HTMLElement|string} backdrop - Elemento backdrop
 * @param {HTMLElement|string} modal - Elemento modal
 * @param {boolean} show - Mostrar u ocultar
 */
export const modalAnimation = (backdrop, modal, show = true) => {
  if (show) {
    gsap.fromTo(backdrop,
      { opacity: 0 },
      { 
        opacity: 1, 
        duration: animationConfig.duration.fast,
        ease: animationConfig.easing.smooth
      }
    );
    
    gsap.fromTo(modal,
      { 
        opacity: 0, 
        scale: 0.8,
        y: -50
      },
      {
        opacity: 1,
        scale: 1,
        y: 0,
        duration: animationConfig.duration.normal,
        ease: animationConfig.easing.bounce
      }
    );
  } else {
    gsap.to(modal, {
      opacity: 0,
      scale: 0.8,
      y: -50,
      duration: animationConfig.duration.fast,
      ease: animationConfig.easing.smooth
    });
    
    gsap.to(backdrop, {
      opacity: 0,
      duration: animationConfig.duration.fast,
      ease: animationConfig.easing.smooth,
      delay: 0.1
    });
  }
};

/**
 * Animación de contador
 * @param {HTMLElement|string} element - Elemento que contiene el número
 * @param {number} from - Número inicial
 * @param {number} to - Número final
 * @param {Object} options - Opciones de animación
 */
export const countUp = (element, from, to, options = {}) => {
  const defaults = {
    duration: animationConfig.duration.slow,
    ease: animationConfig.easing.smooth
  };
  
  const config = { ...defaults, ...options };
  const obj = { value: from };
  
  gsap.to(obj, {
    value: to,
    duration: config.duration,
    ease: config.ease,
    onUpdate: () => {
      const el = typeof element === 'string' ? document.querySelector(element) : element;
      if (el) {
        el.textContent = Math.round(obj.value);
      }
    }
  });
};

/**
 * Hook personalizado para usar animaciones en React
 */
export const useGSAP = () => {
  return {
    fadeInUp,
    slideInLeft,
    scaleIn,
    buttonHover,
    loadingPulse,
    stopLoadingPulse,
    staggerIn,
    notificationSlide,
    modalAnimation,
    countUp,
    gsap
  };
};
