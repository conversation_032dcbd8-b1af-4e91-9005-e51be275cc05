@echo off
echo ========================================
echo 🔧 SOLUCION COMPLETA DE LOGIN
echo ========================================
echo.

echo ⏹️  Reiniciando backend...
docker-compose -f docker-compose.dev.yaml restart backend

echo.
echo ⏳ Esperando que el backend este listo...
timeout /t 10 /nobreak > nul

echo.
echo 📊 Verificando logs del backend...
docker-compose -f docker-compose.dev.yaml logs backend --tail=5

echo.
echo ✅ BACKEND LISTO!
echo.
echo 🌐 Ahora sigue estos pasos:
echo.
echo   1. Ve a: http://localhost:5173/create-superadmin.html
echo   2. Haz clic en "Crear/Actualizar Superadministrador"
echo   3. Haz clic en "Limpiar Storage"
echo   4. Ve a: http://localhost:5173
echo   5. Inicia sesion con:
echo      Email: <EMAIL>
echo      Password: admin123
echo.
echo 🎯 ¡El problema de login deberia estar solucionado!
echo.

echo ¿Quieres abrir las paginas automaticamente? (s/n)
set /p choice=
if /i "%choice%"=="s" (
    echo.
    echo 🌐 Abriendo paginas...
    start http://localhost:5173/create-superadmin.html
    timeout /t 3 /nobreak > nul
    start http://localhost:5173
)

echo.
echo ✅ PROCESO COMPLETADO!
pause
