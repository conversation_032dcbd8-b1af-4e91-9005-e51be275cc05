const db = require('../config/db');
const bcrypt = require('bcryptjs');

async function createTestUser() {
  try {
    console.log('=== Creando usuario de prueba ===');
    
    // Verificar si ya existe
    const existingUser = await db.query(
      'SELECT id FROM usuarios WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (existingUser.rows.length > 0) {
      console.log('Usuario de prueba ya existe, actualizando contraseña...');
      
      // Actualizar contraseña
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('test123', salt);
      
      await db.query(
        'UPDATE usuarios SET password_hash = $1 WHERE email = $2',
        [hashedPassword, '<EMAIL>']
      );
      
      console.log('✅ Contraseña actualizada');
    } else {
      console.log('Creando nuevo usuario de prueba...');
      
      // Crear nuevo usuario
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('test123', salt);
      
      await db.query(`
        INSERT INTO usuarios (nombre, email, password_hash, rol, institucion_id)
        VALUES ($1, $2, $3, $4, $5)
      `, [
        'Usuario de Prueba',
        '<EMAIL>',
        hashedPassword,
        'Director',
        1
      ]);
      
      console.log('✅ Usuario creado');
    }
    
    // También actualizar <NAME_EMAIL>
    console.log('\<NAME_EMAIL>...');
    const salt2 = await bcrypt.genSalt(10);
    const hashedPassword2 = await bcrypt.hash('admin123', salt2);
    
    await db.query(
      'UPDATE usuarios SET password_hash = $1 WHERE email = $2',
      [hashedPassword2, '<EMAIL>']
    );
    
    console.log('✅ Usuario <EMAIL> actualizado');
    
    console.log('\n📋 Credenciales de prueba disponibles:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: test123');
    console.log('   Rol: Director');
    console.log('');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    console.log('   Rol: Superadministrador');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Ejecutar
createTestUser().then(() => process.exit(0));
