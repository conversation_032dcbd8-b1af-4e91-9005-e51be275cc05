import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

const AsignacionesDocentes = () => {
  const [asignaciones, setAsignaciones] = useState([]);
  const [docentes, setDocentes] = useState([]);
  const [grados, setGrados] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    docente_id: '',
    grado_id: '',
    materia: '',
    es_tutor: false
  });
  const { token } = useAuth();

  useEffect(() => {
    fetchData();
  }, [token]);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      const [asignacionesRes, docentesRes, gradosRes] = await Promise.all([
        axios.get(`${API_BASE_URL}/api/grupos/asignaciones`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        axios.get(`${API_BASE_URL}/api/grupos/docentes`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        axios.get(`${API_BASE_URL}/api/grupos/grados`, {
          headers: { Authorization: `Bearer ${token}` }
        })
      ]);

      setAsignaciones(asignacionesRes.data);
      setDocentes(docentesRes.data);
      setGrados(gradosRes.data);
      
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.docente_id || !formData.grado_id) {
      alert('Por favor complete los campos requeridos');
      return;
    }

    try {
      await axios.post(`${API_BASE_URL}/api/grupos/asignaciones`, formData, {
        headers: { Authorization: `Bearer ${token}` }
      });

      alert('Asignación creada exitosamente');
      setFormData({ docente_id: '', grado_id: '', materia: '', es_tutor: false });
      setShowForm(false);
      fetchData();
      
    } catch (error) {
      console.error('Error creating assignment:', error);
      alert('Error creando asignación');
    }
  };

  const handleDelete = async (id) => {
    if (!confirm('¿Está seguro de eliminar esta asignación?')) {
      return;
    }

    try {
      await axios.delete(`${API_BASE_URL}/api/grupos/asignaciones/${id}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      alert('Asignación eliminada exitosamente');
      fetchData();
      
    } catch (error) {
      console.error('Error deleting assignment:', error);
      alert('Error eliminando asignación');
    }
  };

  const groupedAsignaciones = asignaciones.reduce((acc, asignacion) => {
    const key = `${asignacion.grado_nombre} - ${asignacion.grado_nivel}`;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(asignacion);
    return acc;
  }, {});

  if (loading) {
    return (
      <div className="p-4 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-2"></div>
        <p className="text-sm text-slate-600 dark:text-slate-400">Cargando asignaciones...</p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700">
      <div className="p-4 border-b border-slate-200 dark:border-slate-700">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-200">
            Asignaciones de Docentes
          </h3>
          <button
            onClick={() => setShowForm(!showForm)}
            className="px-3 py-1 bg-indigo-600 text-white rounded-lg text-sm hover:bg-indigo-700 transition-colors"
          >
            {showForm ? 'Cancelar' : 'Nueva Asignación'}
          </button>
        </div>
      </div>

      {showForm && (
        <div className="p-4 border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-700">
          <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                Docente *
              </label>
              <select
                value={formData.docente_id}
                onChange={(e) => setFormData(prev => ({ ...prev, docente_id: e.target.value }))}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
                required
              >
                <option value="">Seleccione un docente...</option>
                {docentes.map(docente => (
                  <option key={docente.id} value={docente.id}>
                    {docente.nombre} - {docente.rol}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                Grado *
              </label>
              <select
                value={formData.grado_id}
                onChange={(e) => setFormData(prev => ({ ...prev, grado_id: e.target.value }))}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
                required
              >
                <option value="">Seleccione un grado...</option>
                {grados.map(grado => (
                  <option key={grado.id} value={grado.id}>
                    {grado.nombre} - {grado.nivel} ({grado.alumnos_activos} estudiantes)
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-200 mb-1">
                Materia
              </label>
              <input
                type="text"
                value={formData.materia}
                onChange={(e) => setFormData(prev => ({ ...prev, materia: e.target.value }))}
                placeholder="Ej: Matemáticas, Lenguaje..."
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200"
              />
            </div>

            <div className="flex items-center">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.es_tutor}
                  onChange={(e) => setFormData(prev => ({ ...prev, es_tutor: e.target.checked }))}
                  className="rounded border-slate-300 dark:border-slate-600"
                />
                <span className="text-sm text-slate-700 dark:text-slate-200">Es tutor del grado</span>
              </label>
            </div>

            <div className="md:col-span-2 flex space-x-2">
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Crear Asignación
              </button>
              <button
                type="button"
                onClick={() => setShowForm(false)}
                className="px-4 py-2 bg-slate-500 text-white rounded-lg hover:bg-slate-600 transition-colors"
              >
                Cancelar
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="p-4">
        {Object.keys(groupedAsignaciones).length > 0 ? (
          <div className="space-y-6">
            {Object.entries(groupedAsignaciones).map(([gradoKey, asignacionesGrado]) => (
              <div key={gradoKey} className="border border-slate-200 dark:border-slate-600 rounded-lg p-4">
                <h4 className="font-semibold text-slate-700 dark:text-slate-200 mb-3 flex items-center">
                  <span className="text-lg mr-2">📚</span>
                  {gradoKey}
                  <span className="ml-2 text-xs bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded-full">
                    {asignacionesGrado.length} docente{asignacionesGrado.length !== 1 ? 's' : ''}
                  </span>
                </h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {asignacionesGrado.map(asignacion => (
                    <div
                      key={asignacion.id}
                      className="p-3 border border-slate-200 dark:border-slate-600 rounded-lg bg-slate-50 dark:bg-slate-700"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex-1">
                          <h5 className="font-medium text-slate-700 dark:text-slate-200 text-sm">
                            {asignacion.docente_nombre}
                          </h5>
                          {asignacion.materia && (
                            <p className="text-xs text-slate-500 dark:text-slate-400">
                              {asignacion.materia}
                            </p>
                          )}
                        </div>
                        <button
                          onClick={() => handleDelete(asignacion.id)}
                          className="text-red-500 hover:text-red-700 text-xs"
                          title="Eliminar asignación"
                        >
                          ✕
                        </button>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        {asignacion.es_tutor && (
                          <span className="text-xs bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100 px-2 py-1 rounded-full">
                            Tutor
                          </span>
                        )}
                        {asignacion.docente_telefono && (
                          <span className="text-xs text-slate-500 dark:text-slate-400">
                            📞 {asignacion.docente_telefono}
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-slate-500 dark:text-slate-400">
            <p>No hay asignaciones configuradas</p>
            <p className="text-xs mt-1">Cree asignaciones para organizar los docentes por grado</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AsignacionesDocentes;
