# Override para desarrollo local
version: '3.8'

services:
  # Backend con puerto expuesto para desarrollo
  backend:
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3001
    volumes:
      - ./backend:/usr/src/app
      - /usr/src/app/node_modules
    command: ["node", "server.js"]
    labels: []  # Remover labels de Traefik para desarrollo

  # Frontend con puerto expuesto para desarrollo
  frontend:
    ports:
      - "3000:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3001
    volumes:
      - ./frontend:/usr/src/app
      - /usr/src/app/node_modules
    labels: []  # Remover labels de Traefik para desarrollo

  # PostgreSQL con puerto expuesto para desarrollo
  postgres:
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: ccjapuser
      POSTGRES_PASSWORD: ccjappassword
      POSTGRES_DB: ccjapdb

  # n8n con configuración de desarrollo
  n8n:
    ports:
      - "5678:5678"
    environment:
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - N8N_HOST_WHITELIST=localhost
      - N8N_ENCRYPTION_KEY=K@rur0su24_N8N_ENCRYPT_2025
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_DATABASE=ccjapdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_USER=ccjapuser
      - DB_POSTGRESDB_PASSWORD=ccjappassword
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=K@rur0su24
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=false
    labels: []  # Remover labels de Traefik para desarrollo

# Usar red local en lugar de dokploy-network
networks:
  default:
    driver: bridge
