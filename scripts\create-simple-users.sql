-- Script simple para crear usuarios de prueba
-- Contraseña para todos: admin123

INSERT INTO usuarios (nombre, email, password_hash, rol, institucion_id) VALUES
-- Director
('<PERSON>', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Director', 1),

-- <PERSON><PERSON><PERSON><PERSON>
('<PERSON>', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Coordinador Académico', 1),

-- <PERSON><PERSON><PERSON>
('<PERSON>', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Docente', 1),
('<PERSON>', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Docente', 1),
('Carmen López', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Docente', 1),

-- Secretario
('Rosa Pérez', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Secretario', 1)

ON CONFLICT (email) DO NOTHING;

-- Mostrar usuarios creados
SELECT 'Usuarios creados con éxito:' as mensaje;
SELECT nombre, email, rol FROM usuarios WHERE email LIKE '%@ccjap.edu.sv' ORDER BY rol, nombre;
