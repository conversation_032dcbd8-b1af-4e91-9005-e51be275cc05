import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { Play, RefreshCw, CheckCircle, AlertTriangle, ExternalLink, Workflow } from 'lucide-react';

const N8nWorkflowManager = () => {
  const [workflows, setWorkflows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [status, setStatus] = useState('idle');
  const { token } = useAuth();

  const fetchWorkflows = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/waapi/test-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          n8n_url: 'http://localhost:5678',
          n8n_api_key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0ZjM0ODNlMC0xMjViLTRjMGItYTI4ZS1mNGJkMTJlYTdmNzAiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ4MzI2Mzg0fQ.YHpG_nNe3ssUgOT7JmYm4a5o8VV221oQZHM3F4bIzzM'
        }),
      });

      if (!response.ok) {
        throw new Error('Error al conectar con n8n');
      }

      const data = await response.json();
      
      if (data.results.n8n.status === 'success') {
        setMessage(data.results.n8n.message);
        setStatus('success');
        // Aquí podrías hacer otra llamada para obtener la lista completa de workflows
        setWorkflows([
          {
            id: 'example-1',
            name: 'Notificación de Ausencias',
            description: 'Envía notificaciones automáticas cuando un estudiante falta a clase',
            active: true,
            lastRun: '2025-05-27T06:00:00Z'
          },
          {
            id: 'example-2', 
            name: 'Recordatorio de Tareas',
            description: 'Envía recordatorios de tareas pendientes a estudiantes y padres',
            active: false,
            lastRun: null
          }
        ]);
      } else {
        setMessage(data.results.n8n.message);
        setStatus('error');
      }
    } catch (error) {
      setMessage('Error al conectar con n8n: ' + error.message);
      setStatus('error');
    } finally {
      setLoading(false);
    }
  };

  const executeWorkflow = async (workflowId) => {
    try {
      setLoading(true);
      const response = await fetch('/api/waapi/trigger-workflow', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          workflow_id: workflowId,
          data: {
            test: true,
            timestamp: new Date().toISOString()
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Error al ejecutar workflow');
      }

      const data = await response.json();
      setMessage(`Workflow ${workflowId} ejecutado exitosamente`);
      setStatus('success');
    } catch (error) {
      setMessage('Error al ejecutar workflow: ' + error.message);
      setStatus('error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWorkflows();
  }, []);

  return (
    <div className="p-6 bg-white dark:bg-slate-800 rounded-lg shadow-md">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Workflow className="h-6 w-6 text-indigo-600 mr-2" />
          <h2 className="text-2xl font-bold text-slate-800 dark:text-white">Workflows de n8n</h2>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={fetchWorkflows}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 flex items-center"
            disabled={loading}
          >
            {loading ? <RefreshCw className="animate-spin h-5 w-5 mr-2" /> : <RefreshCw className="h-5 w-5 mr-2" />}
            Actualizar
          </button>
          <a
            href="http://localhost:5678"
            target="_blank"
            rel="noopener noreferrer"
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 flex items-center"
          >
            <ExternalLink className="h-5 w-5 mr-2" />
            Abrir n8n
          </a>
        </div>
      </div>

      {message && (
        <div className={`p-4 mb-6 rounded-md ${status === 'error' ? 'bg-red-50 text-red-700 border border-red-200' : 'bg-green-50 text-green-700 border border-green-200'}`}>
          {status === 'error' ? <AlertTriangle className="inline-block mr-2 h-5 w-5" /> : <CheckCircle className="inline-block mr-2 h-5 w-5" />}
          {message}
        </div>
      )}

      <div className="space-y-4">
        {workflows.length === 0 ? (
          <div className="text-center py-8">
            <Workflow className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No hay workflows configurados</p>
            <p className="text-sm text-gray-400 mt-2">
              Crea workflows en n8n para automatizar notificaciones de WhatsApp
            </p>
          </div>
        ) : (
          workflows.map((workflow) => (
            <div key={workflow.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {workflow.name}
                    </h3>
                    <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                      workflow.active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {workflow.active ? 'Activo' : 'Inactivo'}
                    </span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mt-1">
                    {workflow.description}
                  </p>
                  {workflow.lastRun && (
                    <p className="text-sm text-gray-500 mt-2">
                      Última ejecución: {new Date(workflow.lastRun).toLocaleString()}
                    </p>
                  )}
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => executeWorkflow(workflow.id)}
                    className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 flex items-center"
                    disabled={loading || !workflow.active}
                  >
                    <Play className="h-4 w-4 mr-1" />
                    Ejecutar
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
          Configuración de Workflows
        </h3>
        <p className="text-blue-800 dark:text-blue-200 text-sm mb-3">
          Para crear workflows de automatización de WhatsApp:
        </p>
        <ol className="list-decimal list-inside text-blue-800 dark:text-blue-200 text-sm space-y-1">
          <li>Accede a n8n usando el botón "Abrir n8n"</li>
          <li>Inicia sesión con: <strong><EMAIL></strong> / <strong>K@rur0su24</strong></li>
          <li>Crea un nuevo workflow</li>
          <li>Configura los nodos para recibir datos de webhook</li>
          <li>Agrega nodos de WhatsApp para enviar mensajes</li>
          <li>Activa el workflow</li>
        </ol>
      </div>
    </div>
  );
};

export default N8nWorkflowManager;
