#!/bin/bash

# Script de despliegue para CCJAP Sistema de Gestión Docente
# Uso: ./deploy.sh [dev|prod]

set -e  # Salir si cualquier comando falla

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para imprimir mensajes con colores
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Verificar argumentos
if [ $# -eq 0 ]; then
    print_error "Uso: $0 [dev|prod]"
    exit 1
fi

ENVIRONMENT=$1

# Validar entorno
if [ "$ENVIRONMENT" != "dev" ] && [ "$ENVIRONMENT" != "prod" ]; then
    print_error "Entorno debe ser 'dev' o 'prod'"
    exit 1
fi

print_header "DESPLEGANDO CCJAP SISTEMA - ENTORNO: $ENVIRONMENT"

# Configurar archivos según el entorno
if [ "$ENVIRONMENT" = "dev" ]; then
    COMPOSE_FILE="docker-compose.dev.yaml"
    ENV_FILE=".env.dev"
    print_message "Configurando entorno de desarrollo..."
elif [ "$ENVIRONMENT" = "prod" ]; then
    COMPOSE_FILE="docker-compose.prod.yaml"
    ENV_FILE=".env.prod"
    print_message "Configurando entorno de producción..."
    
    # Verificar que existe el archivo de entorno de producción
    if [ ! -f "$ENV_FILE" ]; then
        print_error "Archivo $ENV_FILE no encontrado."
        print_message "Copia .env.prod.example a .env.prod y configura las variables."
        exit 1
    fi
fi

# Verificar que Docker está ejecutándose
if ! docker info > /dev/null 2>&1; then
    print_error "Docker no está ejecutándose. Por favor, inicia Docker."
    exit 1
fi

# Verificar que docker-compose está disponible
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose no está instalado."
    exit 1
fi

print_message "Verificando archivos necesarios..."

# Verificar que el archivo compose existe
if [ ! -f "$COMPOSE_FILE" ]; then
    print_error "Archivo $COMPOSE_FILE no encontrado."
    exit 1
fi

print_message "Deteniendo contenedores existentes..."
docker-compose -f "$COMPOSE_FILE" down --remove-orphans

if [ "$ENVIRONMENT" = "prod" ]; then
    print_message "Construyendo imágenes para producción..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" build --no-cache
else
    print_message "Construyendo imágenes para desarrollo..."
    docker-compose -f "$COMPOSE_FILE" build
fi

print_message "Iniciando servicios..."
if [ "$ENVIRONMENT" = "prod" ]; then
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
else
    docker-compose -f "$COMPOSE_FILE" up -d
fi

print_message "Esperando que los servicios estén listos..."
sleep 10

print_message "Verificando estado de los contenedores..."
docker-compose -f "$COMPOSE_FILE" ps

# Verificar que los servicios están saludables
print_message "Verificando salud de los servicios..."

# Función para verificar servicio
check_service() {
    local service_name=$1
    local url=$2
    local max_attempts=30
    local attempt=1
    
    print_message "Verificando $service_name..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" > /dev/null 2>&1; then
            print_message "$service_name está funcionando correctamente ✓"
            return 0
        fi
        
        print_warning "Intento $attempt/$max_attempts - $service_name no está listo aún..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name no está respondiendo después de $max_attempts intentos"
    return 1
}

# URLs para verificar según el entorno
if [ "$ENVIRONMENT" = "dev" ]; then
    BACKEND_URL="http://localhost:3001"
    FRONTEND_URL="http://localhost:5173"
    N8N_URL="http://localhost:5678"
else
    # Para producción, usar las URLs de los dominios
    source "$ENV_FILE"
    BACKEND_URL="https://${BACKEND_DOMAIN}"
    FRONTEND_URL="https://${FRONTEND_DOMAIN}"
    N8N_URL="https://${N8N_DOMAIN}"
fi

# Verificar servicios
if [ "$ENVIRONMENT" = "dev" ]; then
    check_service "Backend" "$BACKEND_URL"
    check_service "Frontend" "$FRONTEND_URL"
    check_service "n8n" "$N8N_URL"
fi

print_header "DESPLIEGUE COMPLETADO"

if [ "$ENVIRONMENT" = "dev" ]; then
    print_message "🚀 Aplicación disponible en:"
    print_message "   Frontend: http://localhost:5173"
    print_message "   Backend:  http://localhost:3001"
    print_message "   n8n:      http://localhost:5678"
    print_message "   PostgreSQL: localhost:5432"
    echo ""
    print_message "📋 Credenciales por defecto:"
    print_message "   Superadministrador: <EMAIL> / admin123"
    print_message "   n8n: admin / K@rur0su24"
else
    print_message "🚀 Aplicación desplegada en producción:"
    print_message "   Frontend: https://${FRONTEND_DOMAIN}"
    print_message "   Backend:  https://${BACKEND_DOMAIN}"
    print_message "   n8n:      https://${N8N_DOMAIN}"
    echo ""
    print_message "⚠️  Asegúrate de que Traefik esté configurado correctamente"
    print_message "⚠️  Verifica que los certificados SSL se generen automáticamente"
fi

echo ""
print_message "📊 Para ver logs en tiempo real:"
print_message "   docker-compose -f $COMPOSE_FILE logs -f"
echo ""
print_message "🛑 Para detener la aplicación:"
print_message "   docker-compose -f $COMPOSE_FILE down"

print_message "✅ Despliegue completado exitosamente!"
