import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../context/AuthContext';
import { apiGet, apiPost } from '../utils/api';
import {
  MessageCircle,
  Search,
  Filter,
  Send,
  Paperclip,
  Image,
  FileText,
  Video,
  Mic,
  MoreVertical,
  Reply,
  Forward,
  Trash2,
  Check,
  X,
  Upload,
  Download,
  Eye,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  Calendar,
  Phone
} from 'lucide-react';

const MensajesWhatsAppPage = () => {
  const [mensajes, setMensajes] = useState([]);
  const [conversaciones, setConversaciones] = useState([]);
  const [conversacionActiva, setConversacionActiva] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [busqueda, setBusqueda] = useState('');
  const [filtros, setFiltros] = useState({
    estado: 'todos', // todos, pendientes, aprobados, rechazados
    tipo: 'todos', // todos, texto, imagen, video, audio, documento
    fecha: 'todos' // todos, hoy, semana, mes
  });

  // Estados para el chat
  const [nuevoMensaje, setNuevoMensaje] = useState('');
  const [archivosSeleccionados, setArchivosSeleccionados] = useState([]);
  const [grabandoAudio, setGrabandoAudio] = useState(false);
  const [mostrarEmojiPicker, setMostrarEmojiPicker] = useState(false);

  // Referencias
  const fileInputRef = useRef(null);
  const chatContainerRef = useRef(null);
  const audioRecorderRef = useRef(null);
  const dropZoneRef = useRef(null);

  const { token, currentUser } = useAuth();

  // Verificar permisos del usuario
  const tienePermisos = () => {
    const rolesPermitidos = ['Director', 'Secretaria', 'Superadministrador', 'Director Academico'];
    return rolesPermitidos.includes(currentUser?.rol);
  };

  useEffect(() => {
    if (tienePermisos()) {
      fetchConversaciones();
    }
  }, [token, currentUser]);

  useEffect(() => {
    if (conversacionActiva) {
      fetchMensajesConversacion(conversacionActiva.telefono);
    }
  }, [conversacionActiva]);

  const fetchConversaciones = async () => {
    try {
      setLoading(true);
      const response = await apiGet('/api/whatsapp/conversaciones', token);

      if (!response.ok) {
        throw new Error(`Error al cargar conversaciones: ${response.status}`);
      }

      const data = await response.json();
      setConversaciones(data);

      // Seleccionar la primera conversación por defecto
      if (data.length > 0 && !conversacionActiva) {
        setConversacionActiva(data[0]);
      }

      setLoading(false);
    } catch (err) {
      console.error('Error al cargar conversaciones:', err);
      setError(err.message);
      setLoading(false);
    }
  };

  const fetchMensajesConversacion = async (telefono) => {
    try {
      const response = await apiGet(`/api/whatsapp/mensajes/${telefono}`, token);

      if (!response.ok) {
        throw new Error(`Error al cargar mensajes: ${response.status}`);
      }

      const data = await response.json();
      setMensajes(data);

      // Scroll al final del chat
      setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
        }
      }, 100);
    } catch (err) {
      console.error('Error al cargar mensajes:', err);
      setError(err.message);
    }
  };

  // Filtrar conversaciones
  const conversacionesFiltradas = conversaciones.filter(conv => {
    const matchesBusqueda = !busqueda ||
      conv.nombre?.toLowerCase().includes(busqueda.toLowerCase()) ||
      conv.telefono.includes(busqueda) ||
      conv.ultimoMensaje?.toLowerCase().includes(busqueda.toLowerCase());

    const matchesEstado = filtros.estado === 'todos' || conv.estado === filtros.estado;

    return matchesBusqueda && matchesEstado;
  });

  // Manejar drag and drop
  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (dropZoneRef.current) {
      dropZoneRef.current.classList.add('drag-over');
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (dropZoneRef.current) {
      dropZoneRef.current.classList.remove('drag-over');
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (dropZoneRef.current) {
      dropZoneRef.current.classList.remove('drag-over');
    }

    const files = Array.from(e.dataTransfer.files);
    handleArchivos(files);
  };

  const handleArchivos = (files) => {
    const archivosValidos = files.filter(file => {
      const tiposPermitidos = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'video/mp4', 'video/webm', 'video/ogg',
        'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/mpeg',
        'application/pdf', 'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain'
      ];

      const maxSize = 50 * 1024 * 1024; // 50MB

      return tiposPermitidos.includes(file.type) && file.size <= maxSize;
    });

    setArchivosSeleccionados(prev => [...prev, ...archivosValidos]);
  };

  const eliminarArchivo = (index) => {
    setArchivosSeleccionados(prev => prev.filter((_, i) => i !== index));
  };

  // Enviar mensaje
  const enviarMensaje = async () => {
    if (!nuevoMensaje.trim() && archivosSeleccionados.length === 0) return;
    if (!conversacionActiva) return;

    try {
      const formData = new FormData();
      formData.append('telefono', conversacionActiva.telefono);
      formData.append('mensaje', nuevoMensaje);

      // Determinar tipo de mensaje
      let tipo = 'texto';
      if (archivosSeleccionados.length > 0) {
        const archivo = archivosSeleccionados[0];
        if (archivo.type.startsWith('image/')) tipo = 'imagen';
        else if (archivo.type.startsWith('video/')) tipo = 'video';
        else if (archivo.type.startsWith('audio/')) tipo = 'audio';
        else tipo = 'documento';

        formData.append('tipo', tipo);
        archivosSeleccionados.forEach((archivo, index) => {
          formData.append('archivos', archivo);
        });
      }

      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:3001'}/api/whatsapp/enviar`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Error al enviar mensaje: ${response.status}`);
      }

      const data = await response.json();

      // Limpiar formulario
      setNuevoMensaje('');
      setArchivosSeleccionados([]);

      // Recargar mensajes
      fetchMensajesConversacion(conversacionActiva.telefono);

    } catch (error) {
      console.error('Error al enviar mensaje:', error);
      alert(`Error al enviar mensaje: ${error.message}`);
    }
  };

  // Aprobar mensaje
  const aprobarMensaje = async (mensajeId) => {
    try {
      const response = await apiPost(`/api/whatsapp/aprobar/${mensajeId}`, {}, token);

      if (!response.ok) {
        throw new Error(`Error al aprobar mensaje: ${response.status}`);
      }

      // Recargar mensajes
      fetchMensajesConversacion(conversacionActiva.telefono);

    } catch (error) {
      console.error('Error al aprobar mensaje:', error);
      alert(`Error al aprobar mensaje: ${error.message}`);
    }
  };

  // Reenviar mensaje
  const reenviarMensaje = async (mensajeId) => {
    const telefonos = prompt('Ingresa los números de teléfono separados por comas:');
    if (!telefonos) return;

    const listaTelefonos = telefonos.split(',').map(t => t.trim()).filter(t => t);

    try {
      const response = await apiPost(`/api/whatsapp/reenviar/${mensajeId}`, {
        telefonos: listaTelefonos
      }, token);

      if (!response.ok) {
        throw new Error(`Error al reenviar mensaje: ${response.status}`);
      }

      alert('Mensaje reenviado correctamente');

    } catch (error) {
      console.error('Error al reenviar mensaje:', error);
      alert(`Error al reenviar mensaje: ${error.message}`);
    }
  };

  // Responder mensaje
  const responderMensaje = (mensaje) => {
    setNuevoMensaje(`@${conversacionActiva.telefono} `);
    // Focus en el textarea
    setTimeout(() => {
      const textarea = document.querySelector('textarea[placeholder="Escribe un mensaje..."]');
      if (textarea) {
        textarea.focus();
        textarea.setSelectionRange(textarea.value.length, textarea.value.length);
      }
    }, 100);
  };

  if (!tienePermisos()) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Acceso Denegado</h2>
          <p className="text-gray-600">No tienes permisos para acceder a esta sección.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando conversaciones...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded max-w-md">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <MessageCircle className="h-8 w-8 text-green-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Mensajes WhatsApp</h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Gestiona las conversaciones de WhatsApp
              </p>
            </div>
          </div>

          {/* Filtros rápidos */}
          <div className="flex items-center space-x-4">
            <select
              value={filtros.estado}
              onChange={(e) => setFiltros(prev => ({ ...prev, estado: e.target.value }))}
              className="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white text-sm"
            >
              <option value="todos">Todos los estados</option>
              <option value="pendiente">Pendientes</option>
              <option value="aprobado">Aprobados</option>
              <option value="rechazado">Rechazados</option>
            </select>
          </div>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar - Lista de conversaciones */}
        <div className="w-1/3 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
          {/* Búsqueda */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar conversaciones..."
                value={busqueda}
                onChange={(e) => setBusqueda(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          {/* Lista de conversaciones */}
          <div className="flex-1 overflow-y-auto">
            {conversacionesFiltradas.length === 0 ? (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                No hay conversaciones
              </div>
            ) : (
              conversacionesFiltradas.map((conv) => (
                <div
                  key={conv.telefono}
                  onClick={() => setConversacionActiva(conv)}
                  className={`p-4 border-b border-gray-100 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${
                    conversacionActiva?.telefono === conv.telefono
                      ? 'bg-indigo-50 dark:bg-indigo-900/20 border-r-2 border-indigo-500'
                      : ''
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold">
                        {conv.nombre ? conv.nombre.charAt(0).toUpperCase() : conv.telefono.slice(-2)}
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {conv.nombre || conv.telefono}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {conv.ultimaActividad}
                        </p>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                        {conv.ultimoMensaje}
                      </p>
                      <div className="flex items-center justify-between mt-1">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          conv.estado === 'pendiente' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                          conv.estado === 'aprobado' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                          'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                        }`}>
                          {conv.estado}
                        </span>
                        {conv.mensajesNoLeidos > 0 && (
                          <span className="inline-flex items-center justify-center px-2 py-1 rounded-full text-xs font-bold bg-red-500 text-white">
                            {conv.mensajesNoLeidos}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Área principal del chat */}
        <div className="flex-1 flex flex-col">
          {conversacionActiva ? (
            <>
              {/* Header del chat */}
              <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold">
                      {conversacionActiva.nombre ? conversacionActiva.nombre.charAt(0).toUpperCase() : conversacionActiva.telefono.slice(-2)}
                    </div>
                    <div>
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {conversacionActiva.nombre || conversacionActiva.telefono}
                      </h2>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {conversacionActiva.telefono}
                      </p>
                    </div>
                  </div>

                  {/* Acciones del chat */}
                  <div className="flex items-center space-x-2">
                    <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                      <Phone className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                      <MoreVertical className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Mensajes */}
              <div
                ref={chatContainerRef}
                className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900"
              >
                {mensajes.length === 0 ? (
                  <div className="text-center text-gray-500 dark:text-gray-400 mt-8">
                    No hay mensajes en esta conversación
                  </div>
                ) : (
                  mensajes.map((mensaje, index) => (
                    <div
                      key={mensaje.id}
                      className={`flex ${mensaje.esEnviado ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        mensaje.esEnviado
                          ? 'bg-indigo-600 text-white'
                          : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700'
                      }`}>
                        {/* Contenido del mensaje */}
                        {mensaje.tipo === 'texto' && (
                          <p className="text-sm">{mensaje.contenido}</p>
                        )}

                        {mensaje.tipo === 'imagen' && (
                          <div className="space-y-2">
                            <img
                              src={mensaje.archivoUrl}
                              alt="Imagen"
                              className="rounded-lg max-w-full h-auto cursor-pointer"
                              onClick={() => window.open(mensaje.archivoUrl, '_blank')}
                            />
                            {mensaje.contenido && (
                              <p className="text-sm">{mensaje.contenido}</p>
                            )}
                          </div>
                        )}

                        {mensaje.tipo === 'video' && (
                          <div className="space-y-2">
                            <video
                              src={mensaje.archivoUrl}
                              controls
                              className="rounded-lg max-w-full h-auto"
                            />
                            {mensaje.contenido && (
                              <p className="text-sm">{mensaje.contenido}</p>
                            )}
                          </div>
                        )}

                        {mensaje.tipo === 'audio' && (
                          <div className="space-y-2">
                            <audio
                              src={mensaje.archivoUrl}
                              controls
                              className="w-full"
                            />
                            {mensaje.contenido && (
                              <p className="text-sm">{mensaje.contenido}</p>
                            )}
                          </div>
                        )}

                        {mensaje.tipo === 'documento' && (
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2 p-2 bg-gray-100 dark:bg-gray-700 rounded">
                              <FileText className="h-5 w-5 text-gray-500" />
                              <span className="text-sm font-medium">{mensaje.nombreArchivo}</span>
                              <button
                                onClick={() => window.open(mensaje.archivoUrl, '_blank')}
                                className="text-indigo-600 hover:text-indigo-800"
                              >
                                <Download className="h-4 w-4" />
                              </button>
                            </div>
                            {mensaje.contenido && (
                              <p className="text-sm">{mensaje.contenido}</p>
                            )}
                          </div>
                        )}

                        {/* Timestamp y estado */}
                        <div className={`flex items-center justify-between mt-1 text-xs ${
                          mensaje.esEnviado ? 'text-indigo-200' : 'text-gray-500 dark:text-gray-400'
                        }`}>
                          <span>{new Date(mensaje.fechaEnvio).toLocaleTimeString()}</span>
                          {mensaje.esEnviado && (
                            <div className="flex items-center space-x-1">
                              {mensaje.estado === 'enviado' && <Check className="h-3 w-3" />}
                              {mensaje.estado === 'entregado' && <CheckCircle className="h-3 w-3" />}
                              {mensaje.estado === 'leido' && <CheckCircle className="h-3 w-3 text-blue-300" />}
                            </div>
                          )}
                        </div>

                        {/* Acciones del mensaje */}
                        {!mensaje.esEnviado && (
                          <div className="flex items-center space-x-2 mt-2">
                            <button
                              className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                              onClick={() => responderMensaje(mensaje)}
                            >
                              <Reply className="h-3 w-3 inline mr-1" />
                              Responder
                            </button>
                            <button
                              className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                              onClick={() => reenviarMensaje(mensaje.id)}
                            >
                              <Forward className="h-3 w-3 inline mr-1" />
                              Reenviar
                            </button>
                            {(currentUser?.rol === 'Director' || currentUser?.rol === 'Superadministrador') && (
                              <button
                                className="text-xs text-green-500 hover:text-green-700"
                                onClick={() => aprobarMensaje(mensaje.id)}
                              >
                                <Check className="h-3 w-3 inline mr-1" />
                                Aprobar
                              </button>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>

              {/* Área de archivos seleccionados */}
              {archivosSeleccionados.length > 0 && (
                <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
                  <div className="flex items-center space-x-2 overflow-x-auto">
                    {archivosSeleccionados.map((archivo, index) => (
                      <div key={index} className="relative flex-shrink-0">
                        <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                          {archivo.type.startsWith('image/') ? (
                            <img
                              src={URL.createObjectURL(archivo)}
                              alt="Preview"
                              className="w-full h-full object-cover rounded-lg"
                            />
                          ) : archivo.type.startsWith('video/') ? (
                            <Video className="h-6 w-6 text-gray-500" />
                          ) : archivo.type.startsWith('audio/') ? (
                            <Mic className="h-6 w-6 text-gray-500" />
                          ) : (
                            <FileText className="h-6 w-6 text-gray-500" />
                          )}
                        </div>
                        <button
                          onClick={() => eliminarArchivo(index)}
                          className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Área de composición */}
              <div
                ref={dropZoneRef}
                className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4 relative"
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <div className="flex items-end space-x-2">
                  {/* Botón de archivos */}
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <Paperclip className="h-5 w-5" />
                  </button>

                  {/* Input de archivo oculto */}
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
                    onChange={(e) => handleArchivos(Array.from(e.target.files))}
                    className="hidden"
                  />

                  {/* Campo de texto */}
                  <div className="flex-1">
                    <textarea
                      value={nuevoMensaje}
                      onChange={(e) => setNuevoMensaje(e.target.value)}
                      placeholder="Escribe un mensaje..."
                      className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none dark:bg-gray-700 dark:text-white"
                      rows="1"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          enviarMensaje();
                        }
                      }}
                    />
                  </div>

                  {/* Botón de envío */}
                  <button
                    onClick={enviarMensaje}
                    disabled={!nuevoMensaje.trim() && archivosSeleccionados.length === 0}
                    className="p-2 bg-indigo-600 text-white rounded-full hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Send className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
              <div className="text-center">
                <MessageCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Selecciona una conversación
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  Elige una conversación de la lista para comenzar a chatear
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Estilos CSS para drag and drop */}
      <style jsx>{`
        .drag-over {
          background-color: rgba(99, 102, 241, 0.1);
          border-color: rgb(99, 102, 241);
        }

        .drag-drop-overlay.show {
          display: flex !important;
        }
      `}</style>
    </div>
  );
};

export default MensajesWhatsAppPage;