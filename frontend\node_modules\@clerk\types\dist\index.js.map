{"version": 3, "sources": ["../src/index.ts", "../src/oauth.ts", "../src/saml.ts", "../src/web3.ts"], "sourcesContent": ["export * from './api';\nexport * from './appearance';\nexport * from './elementIds';\nexport * from './attributes';\nexport * from './authConfig';\nexport * from './backupCode';\nexport * from './clerk';\nexport * from './client';\nexport * from './commerce';\nexport * from './commerceSettings';\nexport * from './deletedObject';\nexport * from './displayConfig';\nexport * from './emailAddress';\nexport * from './environment';\nexport * from './externalAccount';\nexport * from './enterpriseAccount';\nexport * from './factors';\nexport * from './hooks';\nexport * from './identificationLink';\nexport * from './identifiers';\nexport * from './image';\nexport * from './instance';\nexport * from './json';\nexport * from './jwt';\nexport * from './key';\nexport * from './localization';\nexport * from './jwtv2';\nexport * from './multiDomain';\nexport * from './oauth';\nexport * from './organization';\nexport * from './organizationDomain';\nexport * from './organizationInvitation';\nexport * from './organizationMembership';\nexport * from './organizationMembershipRequest';\nexport * from './organizationSettings';\nexport * from './organizationSuggestion';\nexport * from './passwords';\nexport * from './permission';\nexport * from './phoneNumber';\nexport * from './redirects';\nexport * from './resource';\nexport * from './role';\nexport * from './router';\nexport * from './saml';\nexport * from './samlAccount';\nexport * from './session';\nexport * from './sessionVerification';\nexport * from './signIn';\nexport * from './signUp';\nexport * from './ssr';\nexport * from './strategies';\nexport * from './theme';\nexport * from './token';\nexport * from './totp';\nexport * from './telemetry';\nexport * from './user';\nexport * from './userOrganizationInvitation';\nexport * from './userSettings';\nexport * from './utils';\nexport * from './verification';\nexport * from './web3';\nexport * from './web3Wallet';\nexport * from './customPages';\nexport * from './pagination';\nexport * from './passkey';\nexport * from './customMenuItems';\nexport * from './samlConnection';\nexport * from './waitlist';\nexport * from './snapshots';\nexport * from './authObject';\nexport * from './phoneCodeChannel';\n", "import type { OAuthStrategy } from './strategies';\n\nexport type OAuthScope = string;\n\nexport interface OAuthProviderData {\n  provider: OAuthProvider;\n  strategy: OAuthStrategy;\n  name: string;\n  docsUrl: string;\n}\n\nexport type FacebookOauthProvider = 'facebook';\nexport type GoogleOauthProvider = 'google';\nexport type HubspotOauthProvider = 'hubspot';\nexport type GithubOauthProvider = 'github';\nexport type TiktokOauthProvider = 'tiktok';\nexport type GitlabOauthProvider = 'gitlab';\nexport type DiscordOauthProvider = 'discord';\nexport type TwitterOauthProvider = 'twitter';\nexport type TwitchOauthProvider = 'twitch';\nexport type LinkedinOauthProvider = 'linkedin';\nexport type LinkedinOIDCOauthProvider = 'linkedin_oidc';\nexport type DropboxOauthProvider = 'dropbox';\nexport type AtlassianOauthProvider = 'atlassian';\nexport type BitbucketOauthProvider = 'bitbucket';\nexport type MicrosoftOauthProvider = 'microsoft';\nexport type NotionOauthProvider = 'notion';\nexport type AppleOauthProvider = 'apple';\nexport type LineOauthProvider = 'line';\nexport type InstagramOauthProvider = 'instagram';\nexport type CoinbaseOauthProvider = 'coinbase';\nexport type SpotifyOauthProvider = 'spotify';\nexport type XeroOauthProvider = 'xero';\nexport type BoxOauthProvider = 'box';\nexport type SlackOauthProvider = 'slack';\nexport type LinearOauthProvider = 'linear';\nexport type XOauthProvider = 'x';\nexport type EnstallOauthProvider = 'enstall';\nexport type HuggingfaceOAuthProvider = 'huggingface';\nexport type CustomOauthProvider = `custom_${string}`;\n\nexport type OAuthProvider =\n  | FacebookOauthProvider\n  | GoogleOauthProvider\n  | HubspotOauthProvider\n  | GithubOauthProvider\n  | TiktokOauthProvider\n  | GitlabOauthProvider\n  | DiscordOauthProvider\n  | TwitterOauthProvider\n  | TwitchOauthProvider\n  | LinkedinOauthProvider\n  | LinkedinOIDCOauthProvider\n  | DropboxOauthProvider\n  | AtlassianOauthProvider\n  | BitbucketOauthProvider\n  | MicrosoftOauthProvider\n  | NotionOauthProvider\n  | AppleOauthProvider\n  | LineOauthProvider\n  | InstagramOauthProvider\n  | CoinbaseOauthProvider\n  | SpotifyOauthProvider\n  | XeroOauthProvider\n  | BoxOauthProvider\n  | SlackOauthProvider\n  | LinearOauthProvider\n  | XOauthProvider\n  | EnstallOauthProvider\n  | HuggingfaceOAuthProvider\n  | CustomOauthProvider;\n\n/**\n * @deprecated Use `import { OAUTH_PROVIDERS } from \"@clerk/shared/oauth\"` instead.\n *\n * @hidden\n */\nexport const OAUTH_PROVIDERS: OAuthProviderData[] = [\n  {\n    provider: 'google',\n    strategy: 'oauth_google',\n    name: 'Google',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/google',\n  },\n  {\n    provider: 'discord',\n    strategy: 'oauth_discord',\n    name: 'Discord',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/discord',\n  },\n  {\n    provider: 'facebook',\n    strategy: 'oauth_facebook',\n    name: 'Facebook',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/facebook',\n  },\n  {\n    provider: 'twitch',\n    strategy: 'oauth_twitch',\n    name: 'Twitch',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/twitch',\n  },\n  {\n    provider: 'twitter',\n    strategy: 'oauth_twitter',\n    name: 'Twitter',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/twitter',\n  },\n  {\n    provider: 'microsoft',\n    strategy: 'oauth_microsoft',\n    name: 'Microsoft',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/microsoft',\n  },\n  {\n    provider: 'tiktok',\n    strategy: 'oauth_tiktok',\n    name: 'TikTok',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/tiktok',\n  },\n  {\n    provider: 'linkedin',\n    strategy: 'oauth_linkedin',\n    name: 'LinkedIn',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/linkedin',\n  },\n  {\n    provider: 'linkedin_oidc',\n    strategy: 'oauth_linkedin_oidc',\n    name: 'LinkedIn',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/linkedin-oidc',\n  },\n  {\n    provider: 'github',\n    strategy: 'oauth_github',\n    name: 'GitHub',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/github',\n  },\n  {\n    provider: 'gitlab',\n    strategy: 'oauth_gitlab',\n    name: 'GitLab',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/gitlab',\n  },\n  {\n    provider: 'dropbox',\n    strategy: 'oauth_dropbox',\n    name: 'Dropbox',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/dropbox',\n  },\n  {\n    provider: 'atlassian',\n    strategy: 'oauth_atlassian',\n    name: 'Atlassian',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/atlassian',\n  },\n  {\n    provider: 'bitbucket',\n    strategy: 'oauth_bitbucket',\n    name: 'Bitbucket',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/bitbucket',\n  },\n  {\n    provider: 'hubspot',\n    strategy: 'oauth_hubspot',\n    name: 'HubSpot',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/hubspot',\n  },\n  {\n    provider: 'notion',\n    strategy: 'oauth_notion',\n    name: 'Notion',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/notion',\n  },\n  {\n    provider: 'apple',\n    strategy: 'oauth_apple',\n    name: 'Apple',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/apple',\n  },\n  {\n    provider: 'line',\n    strategy: 'oauth_line',\n    name: 'LINE',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/line',\n  },\n  {\n    provider: 'instagram',\n    strategy: 'oauth_instagram',\n    name: 'Instagram',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/instagram',\n  },\n  {\n    provider: 'coinbase',\n    strategy: 'oauth_coinbase',\n    name: 'Coinbase',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/coinbase',\n  },\n  {\n    provider: 'spotify',\n    strategy: 'oauth_spotify',\n    name: 'Spotify',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/spotify',\n  },\n  {\n    provider: 'xero',\n    strategy: 'oauth_xero',\n    name: 'Xero',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/xero',\n  },\n  {\n    provider: 'box',\n    strategy: 'oauth_box',\n    name: 'Box',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/box',\n  },\n  {\n    provider: 'slack',\n    strategy: 'oauth_slack',\n    name: 'Slack',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/slack',\n  },\n  {\n    provider: 'linear',\n    strategy: 'oauth_linear',\n    name: 'Linear',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/linear',\n  },\n  {\n    provider: 'x',\n    strategy: 'oauth_x',\n    name: 'X / Twitter',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/x-twitter-v2',\n  },\n  {\n    provider: 'enstall',\n    strategy: 'oauth_enstall',\n    name: 'Enstall',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/enstall',\n  },\n  {\n    provider: 'huggingface',\n    strategy: 'oauth_huggingface',\n    name: 'Hugging Face',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/huggingface',\n  },\n];\n\ninterface getOAuthProviderDataProps {\n  provider?: OAuthProvider;\n  strategy?: OAuthStrategy;\n}\n\n/**\n * @deprecated This utility will be dropped in the next major release.\n *\n * @hidden\n */\nexport function getOAuthProviderData({\n  provider,\n  strategy,\n}: getOAuthProviderDataProps): OAuthProviderData | undefined | null {\n  if (provider) {\n    return OAUTH_PROVIDERS.find(oauth_provider => oauth_provider.provider == provider);\n  }\n\n  return OAUTH_PROVIDERS.find(oauth_provider => oauth_provider.strategy == strategy);\n}\n\n/**\n * @deprecated This utility will be dropped in the next major release.\n *\n * @hidden\n */\nexport function sortedOAuthProviders(sortingArray: OAuthStrategy[]) {\n  return OAUTH_PROVIDERS.slice().sort((a, b) => {\n    let aPos = sortingArray.indexOf(a.strategy);\n    if (aPos == -1) {\n      aPos = Number.MAX_SAFE_INTEGER;\n    }\n\n    let bPos = sortingArray.indexOf(b.strategy);\n    if (bPos == -1) {\n      bPos = Number.MAX_SAFE_INTEGER;\n    }\n\n    return aPos - bPos;\n  });\n}\n", "export type SamlIdpSlug = 'saml_okta' | 'saml_google' | 'saml_microsoft' | 'saml_custom';\n\nexport type SamlIdp = {\n  name: string;\n  logo: string;\n};\n\nexport type SamlIdpMap = Record<SamlIdpSlug, SamlIdp>;\n\nexport const SAML_IDPS: SamlIdpMap = {\n  saml_okta: {\n    name: 'Okta Workforce',\n    logo: 'okta',\n  },\n  saml_google: {\n    name: 'Google Workspace',\n    logo: 'google',\n  },\n  saml_microsoft: {\n    name: 'Microsoft Entra ID (Formerly AD)',\n    logo: 'azure',\n  },\n  saml_custom: {\n    name: 'SAML',\n    logo: 'saml',\n  },\n};\n", "import type { Web3Strategy } from './strategies';\n\nexport interface Web3ProviderData {\n  provider: Web3Provider;\n  strategy: Web3Strategy;\n  name: string;\n}\n\nexport type MetamaskWeb3Provider = 'metamask';\nexport type CoinbaseWalletWeb3Provider = 'coinbase_wallet';\nexport type OKXWalletWeb3Provider = 'okx_wallet';\n\nexport type Web3Provider = MetamaskWeb3Provider | CoinbaseWalletWeb3Provider | OKXWalletWeb3Provider;\n\n/**\n * @deprecated Use `import { WEB3_PROVIDERS } from \"@clerk/shared/web3\"` instead.\n *\n * @hidden\n */\nexport const WEB3_PROVIDERS: Web3ProviderData[] = [\n  {\n    provider: 'metamask',\n    strategy: 'web3_metamask_signature',\n    name: 'MetaMask',\n  },\n  {\n    provider: 'coinbase_wallet',\n    strategy: 'web3_coinbase_wallet_signature',\n    name: 'Coinbase Wallet',\n  },\n  {\n    provider: 'okx_wallet',\n    strategy: 'web3_okx_wallet_signature',\n    name: 'OKX Wallet',\n  },\n];\n\ninterface getWeb3ProviderDataProps {\n  provider?: Web3Provider;\n  strategy?: Web3Strategy;\n}\n\n/**\n * @deprecated This utility will be dropped in the next major release.\n *\n * @hidden\n */\nexport function getWeb3ProviderData({\n  provider,\n  strategy,\n}: getWeb3ProviderDataProps): Web3ProviderData | undefined | null {\n  if (provider) {\n    return WEB3_PROVIDERS.find(p => p.provider == provider);\n  }\n\n  return WEB3_PROVIDERS.find(p => p.strategy == strategy);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AC6EO,IAAM,kBAAuC;AAAA,EACl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qBAAqB;AAAA,EACnC;AAAA,EACA;AACF,GAAoE;AAClE,MAAI,UAAU;AACZ,WAAO,gBAAgB,KAAK,oBAAkB,eAAe,YAAY,QAAQ;AAAA,EACnF;AAEA,SAAO,gBAAgB,KAAK,oBAAkB,eAAe,YAAY,QAAQ;AACnF;AAOO,SAAS,qBAAqB,cAA+B;AAClE,SAAO,gBAAgB,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM;AAC5C,QAAI,OAAO,aAAa,QAAQ,EAAE,QAAQ;AAC1C,QAAI,QAAQ,IAAI;AACd,aAAO,OAAO;AAAA,IAChB;AAEA,QAAI,OAAO,aAAa,QAAQ,EAAE,QAAQ;AAC1C,QAAI,QAAQ,IAAI;AACd,aAAO,OAAO;AAAA,IAChB;AAEA,WAAO,OAAO;AAAA,EAChB,CAAC;AACH;;;ACvRO,IAAM,YAAwB;AAAA,EACnC,WAAW;AAAA,IACT,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AACF;;;ACPO,IAAM,iBAAqC;AAAA,EAChD;AAAA,IACE,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AACF;AAYO,SAAS,oBAAoB;AAAA,EAClC;AAAA,EACA;AACF,GAAkE;AAChE,MAAI,UAAU;AACZ,WAAO,eAAe,KAAK,OAAK,EAAE,YAAY,QAAQ;AAAA,EACxD;AAEA,SAAO,eAAe,KAAK,OAAK,EAAE,YAAY,QAAQ;AACxD;", "names": []}