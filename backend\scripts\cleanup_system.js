const fs = require('fs');
const path = require('path');

// Archivos y directorios a eliminar
const filesToDelete = [
  // Scripts antiguos que ya no necesitamos
  'scripts/setup_n8n_credentials.js',
  'scripts/create_basic_workflows.js',
  'scripts/create_whatsapp_ai_workflow.js',
  
  // Rutas que pueden estar duplicadas o no usarse
  'routes/whatsapp-ai.js', // Reemplazado por el workflow de n8n
  
  // Archivos temporales o de prueba
  'test.js',
  'temp.js',
  '.env.example',
  
  // Logs antiguos
  'logs/',
  'debug.log',
  'error.log',
  
  // Archivos de configuración innecesarios
  '.eslintrc.js',
  '.prettierrc',
  'jest.config.js'
];

// Directorios del frontend a limpiar
const frontendFilesToDelete = [
  // Componentes no utilizados
  'src/components/unused/',
  'src/pages/TestPage.jsx',
  
  // Archivos de prueba
  'src/test/',
  'src/__tests__/',
  
  // Archivos de configuración innecesarios
  '.eslintrc.js',
  '.prettierrc',
  'jest.config.js',
  
  // Archivos de build antiguos
  'dist/',
  'build/',
  
  // Dependencias de desarrollo no necesarias
  'node_modules/.cache/',
  
  // Archivos temporales
  '.tmp/',
  'temp/',
  
  // Logs
  'logs/',
  'debug.log',
  'npm-debug.log*',
  'yarn-debug.log*',
  'yarn-error.log*'
];

function deleteFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      if (stats.isDirectory()) {
        fs.rmSync(filePath, { recursive: true, force: true });
        console.log(`🗑️  Directorio eliminado: ${filePath}`);
      } else {
        fs.unlinkSync(filePath);
        console.log(`🗑️  Archivo eliminado: ${filePath}`);
      }
      return true;
    }
    return false;
  } catch (error) {
    console.error(`❌ Error eliminando ${filePath}:`, error.message);
    return false;
  }
}

function cleanupBackend() {
  console.log('\n=== Limpiando Backend ===');
  let deletedCount = 0;
  
  filesToDelete.forEach(file => {
    const fullPath = path.join(__dirname, '..', file);
    if (deleteFile(fullPath)) {
      deletedCount++;
    }
  });
  
  console.log(`✅ Backend: ${deletedCount} archivos/directorios eliminados`);
  return deletedCount;
}

function cleanupFrontend() {
  console.log('\n=== Limpiando Frontend ===');
  let deletedCount = 0;
  
  const frontendPath = path.join(__dirname, '..', '..', 'frontend');
  
  frontendFilesToDelete.forEach(file => {
    const fullPath = path.join(frontendPath, file);
    if (deleteFile(fullPath)) {
      deletedCount++;
    }
  });
  
  console.log(`✅ Frontend: ${deletedCount} archivos/directorios eliminados`);
  return deletedCount;
}

function cleanupNodeModules() {
  console.log('\n=== Limpiando Cache de Node Modules ===');
  let deletedCount = 0;
  
  // Limpiar cache del backend
  const backendCachePaths = [
    path.join(__dirname, '..', 'node_modules', '.cache'),
    path.join(__dirname, '..', '.npm'),
    path.join(__dirname, '..', '.yarn')
  ];
  
  // Limpiar cache del frontend
  const frontendCachePaths = [
    path.join(__dirname, '..', '..', 'frontend', 'node_modules', '.cache'),
    path.join(__dirname, '..', '..', 'frontend', '.npm'),
    path.join(__dirname, '..', '..', 'frontend', '.yarn'),
    path.join(__dirname, '..', '..', 'frontend', 'node_modules', '.vite')
  ];
  
  [...backendCachePaths, ...frontendCachePaths].forEach(cachePath => {
    if (deleteFile(cachePath)) {
      deletedCount++;
    }
  });
  
  console.log(`✅ Cache: ${deletedCount} directorios de cache eliminados`);
  return deletedCount;
}

function cleanupUploads() {
  console.log('\n=== Limpiando Archivos de Upload Antiguos ===');
  let deletedCount = 0;
  
  const uploadsPath = path.join(__dirname, '..', 'uploads');
  
  if (fs.existsSync(uploadsPath)) {
    const files = fs.readdirSync(uploadsPath);
    const now = Date.now();
    const oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000); // 7 días
    
    files.forEach(file => {
      const filePath = path.join(uploadsPath, file);
      const stats = fs.statSync(filePath);
      
      // Eliminar archivos más antiguos de 7 días
      if (stats.mtime.getTime() < oneWeekAgo) {
        if (deleteFile(filePath)) {
          deletedCount++;
        }
      }
    });
  }
  
  console.log(`✅ Uploads: ${deletedCount} archivos antiguos eliminados`);
  return deletedCount;
}

function getDirectorySize(dirPath) {
  let totalSize = 0;
  
  if (!fs.existsSync(dirPath)) {
    return 0;
  }
  
  function calculateSize(currentPath) {
    const stats = fs.statSync(currentPath);
    
    if (stats.isFile()) {
      totalSize += stats.size;
    } else if (stats.isDirectory()) {
      const files = fs.readdirSync(currentPath);
      files.forEach(file => {
        calculateSize(path.join(currentPath, file));
      });
    }
  }
  
  try {
    calculateSize(dirPath);
  } catch (error) {
    console.error(`Error calculando tamaño de ${dirPath}:`, error.message);
  }
  
  return totalSize;
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showSpaceReport() {
  console.log('\n=== Reporte de Espacio ===');
  
  const backendPath = path.join(__dirname, '..');
  const frontendPath = path.join(__dirname, '..', '..', 'frontend');
  
  const backendSize = getDirectorySize(backendPath);
  const frontendSize = getDirectorySize(frontendPath);
  const totalSize = backendSize + frontendSize;
  
  console.log(`📊 Backend: ${formatBytes(backendSize)}`);
  console.log(`📊 Frontend: ${formatBytes(frontendSize)}`);
  console.log(`📊 Total del proyecto: ${formatBytes(totalSize)}`);
  
  // Mostrar los directorios más grandes
  const nodeModulesBackend = getDirectorySize(path.join(backendPath, 'node_modules'));
  const nodeModulesFrontend = getDirectorySize(path.join(frontendPath, 'node_modules'));
  
  console.log(`\n📦 node_modules backend: ${formatBytes(nodeModulesBackend)}`);
  console.log(`📦 node_modules frontend: ${formatBytes(nodeModulesFrontend)}`);
}

// Función principal
function cleanupSystem() {
  console.log('🧹 === LIMPIEZA DEL SISTEMA CCJAP ===');
  
  // Mostrar espacio antes de la limpieza
  console.log('\n📊 ANTES DE LA LIMPIEZA:');
  showSpaceReport();
  
  // Realizar limpieza
  const backendDeleted = cleanupBackend();
  const frontendDeleted = cleanupFrontend();
  const cacheDeleted = cleanupNodeModules();
  const uploadsDeleted = cleanupUploads();
  
  const totalDeleted = backendDeleted + frontendDeleted + cacheDeleted + uploadsDeleted;
  
  // Mostrar espacio después de la limpieza
  console.log('\n📊 DESPUÉS DE LA LIMPIEZA:');
  showSpaceReport();
  
  console.log('\n=== RESUMEN DE LIMPIEZA ===');
  console.log(`🗑️  Total de archivos/directorios eliminados: ${totalDeleted}`);
  console.log('✅ Sistema limpio y optimizado');
  
  console.log('\n📋 ARCHIVOS IMPORTANTES CONSERVADOS:');
  console.log('✅ Configuraciones de base de datos');
  console.log('✅ Rutas principales del API');
  console.log('✅ Componentes del frontend en uso');
  console.log('✅ Archivos de configuración esenciales');
  console.log('✅ Workflows de n8n');
  
  return totalDeleted;
}

// Ejecutar si se llama directamente
if (require.main === module) {
  cleanupSystem();
}

module.exports = { cleanupSystem };
