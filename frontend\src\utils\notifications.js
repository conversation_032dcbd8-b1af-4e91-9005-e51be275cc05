// Sistema de notificaciones para la aplicación CCJAP
// Proporciona funciones para mostrar notificaciones toast y alertas

/**
 * Muestra una notificación toast
 * @param {string} message - Mensaje a mostrar
 * @param {string} type - Tipo de notificación: 'success', 'error', 'warning', 'info'
 * @param {number} duration - Duración en milisegundos (por defecto 3000)
 */
export const showNotification = (message, type = 'info', duration = 3000) => {
  // Crear el contenedor de notificaciones si no existe
  let container = document.getElementById('notification-container');
  if (!container) {
    container = document.createElement('div');
    container.id = 'notification-container';
    container.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      pointer-events: none;
    `;
    document.body.appendChild(container);
  }

  // Crear la notificación
  const notification = document.createElement('div');
  notification.style.cssText = `
    background: ${getBackgroundColor(type)};
    color: ${getTextColor(type)};
    padding: 12px 16px;
    margin-bottom: 8px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid ${getBorderColor(type)};
    max-width: 400px;
    word-wrap: break-word;
    pointer-events: auto;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    display: flex;
    align-items: center;
    gap: 8px;
  `;

  // Agregar icono según el tipo
  const icon = getIcon(type);
  notification.innerHTML = `
    <span style="font-size: 16px;">${icon}</span>
    <span>${message}</span>
    <button onclick="this.parentElement.remove()" style="
      background: none;
      border: none;
      color: inherit;
      cursor: pointer;
      font-size: 18px;
      margin-left: auto;
      padding: 0;
      opacity: 0.7;
    ">×</button>
  `;

  // Agregar al contenedor
  container.appendChild(notification);

  // Animar entrada
  setTimeout(() => {
    notification.style.transform = 'translateX(0)';
  }, 10);

  // Auto-remover después de la duración especificada
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 300);
  }, duration);

  // Log para debugging
  console.log(`[Notification ${type.toUpperCase()}]:`, message);
};

/**
 * Obtiene el color de fondo según el tipo
 */
function getBackgroundColor(type) {
  const colors = {
    success: '#f0f9ff',
    error: '#fef2f2',
    warning: '#fffbeb',
    info: '#f0f9ff'
  };
  return colors[type] || colors.info;
}

/**
 * Obtiene el color del texto según el tipo
 */
function getTextColor(type) {
  const colors = {
    success: '#065f46',
    error: '#991b1b',
    warning: '#92400e',
    info: '#1e40af'
  };
  return colors[type] || colors.info;
}

/**
 * Obtiene el color del borde según el tipo
 */
function getBorderColor(type) {
  const colors = {
    success: '#10b981',
    error: '#ef4444',
    warning: '#f59e0b',
    info: '#3b82f6'
  };
  return colors[type] || colors.info;
}

/**
 * Obtiene el icono según el tipo
 */
function getIcon(type) {
  const icons = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️'
  };
  return icons[type] || icons.info;
}

/**
 * Muestra una notificación de éxito
 */
export const showSuccess = (message, duration = 3000) => {
  showNotification(message, 'success', duration);
};

/**
 * Muestra una notificación de error
 */
export const showError = (message, duration = 5000) => {
  showNotification(message, 'error', duration);
};

/**
 * Muestra una notificación de advertencia
 */
export const showWarning = (message, duration = 4000) => {
  showNotification(message, 'warning', duration);
};

/**
 * Muestra una notificación informativa
 */
export const showInfo = (message, duration = 3000) => {
  showNotification(message, 'info', duration);
};

/**
 * Limpia todas las notificaciones
 */
export const clearAllNotifications = () => {
  const container = document.getElementById('notification-container');
  if (container) {
    container.innerHTML = '';
  }
};

/**
 * Muestra una confirmación con callback
 */
export const showConfirmation = (message, onConfirm, onCancel = null) => {
  const confirmed = window.confirm(message);
  if (confirmed && onConfirm) {
    onConfirm();
  } else if (!confirmed && onCancel) {
    onCancel();
  }
  return confirmed;
};

// Exportar por defecto la función principal
export default showNotification;
