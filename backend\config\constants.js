// Constantes del sistema para asegurar consistencia
require('dotenv').config();

// JWT Configuration - VALOR FIJO PARA DESARROLLO
const JWT_SECRET = process.env.JWT_SECRET || 'K@rur0su24_JWT_SECRET_2025';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

// Database Configuration
const DB_CONFIG = {
  user: process.env.DB_USER || 'ccjapuser',
  password: process.env.DB_PASSWORD || 'ccjappassword',
  database: process.env.DB_NAME || 'ccjapdb',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432
};

// n8n Configuration
const N8N_CONFIG = {
  url: process.env.N8N_URL || 'http://localhost:5678',
  apiKey: process.env.N8N_API_KEY || '',
  webhookUrl: process.env.N8N_WEBHOOK_URL || 'http://localhost:5678/webhook'
};

// WhatsApp API Configuration
const WHATSAPP_CONFIG = {
  apiKey: process.env.WHATSAPP_API_KEY || '',
  phoneNumber: process.env.WHATSAPP_PHONE_NUMBER || '',
  webhookUrl: process.env.WHATSAPP_WEBHOOK_URL || 'http://localhost:3001/api/waapi/webhook'
};

// Sistema de Roles y Permisos
const ROLES = {
  SUPERADMINISTRADOR: 'Superadministrador',
  DIRECTOR: 'Director',
  COORDINADOR_ACADEMICO: 'Coordinador Académico',
  DOCENTE: 'Docente',
  SECRETARIO: 'Secretario'
};

// Permisos por rol
const PERMISSIONS = {
  [ROLES.SUPERADMINISTRADOR]: {
    // Acceso COMPLETO a todo
    usuarios: ['create', 'read', 'update', 'delete'],
    alumnos: ['create', 'read', 'update', 'delete'],
    grados: ['create', 'read', 'update', 'delete'],
    ausencias: ['create', 'read', 'update', 'delete'],
    whatsapp: ['create', 'read', 'update', 'delete'],
    configuracion: ['create', 'read', 'update', 'delete'],
    configuracion_avanzada: ['create', 'read', 'update', 'delete'],
    academic: ['create', 'read', 'update', 'delete'],
    system: ['create', 'read', 'update', 'delete'],
    database: ['create', 'read', 'update', 'delete'],
    n8n: ['create', 'read', 'update', 'delete']
  },
  [ROLES.DIRECTOR]: {
    // Acceso administrativo pero NO a configuraciones técnicas
    usuarios: ['create', 'read', 'update', 'delete'],
    alumnos: ['create', 'read', 'update', 'delete'],
    grados: ['create', 'read', 'update', 'delete'],
    ausencias: ['read', 'update'],
    whatsapp: ['read'],
    configuracion: ['read', 'update'], // Solo configuración básica
    academic: ['create', 'read', 'update', 'delete']
  },
  [ROLES.COORDINADOR_ACADEMICO]: {
    alumnos: ['create', 'read', 'update', 'delete'],
    grados: ['read', 'update'],
    ausencias: ['create', 'read', 'update'],
    academic: ['create', 'read', 'update', 'delete'],
    whatsapp: ['read']
  },
  [ROLES.DOCENTE]: {
    alumnos: ['read'], // Solo sus alumnos
    ausencias: ['create', 'read'], // Solo de sus alumnos
    academic: ['read'], // Solo sus asignaciones
    whatsapp: ['read'] // Solo comunicaciones de sus alumnos
  },
  [ROLES.SECRETARIO]: {
    alumnos: ['create', 'read', 'update'],
    ausencias: ['create', 'read', 'update'],
    whatsapp: ['read', 'update']
  }
};

// Función para verificar permisos
const hasPermission = (userRole, resource, action) => {
  const rolePermissions = PERMISSIONS[userRole];
  if (!rolePermissions) return false;
  
  const resourcePermissions = rolePermissions[resource];
  if (!resourcePermissions) return false;
  
  return resourcePermissions.includes(action);
};

// Función para verificar si es Superadministrador
const isSuperAdmin = (userRole) => {
  return userRole === ROLES.SUPERADMINISTRADOR;
};

// Configuración de archivos
const UPLOAD_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
  uploadDir: process.env.UPLOAD_DIR || './uploads'
};

// Configuración de logs
const LOG_CONFIG = {
  level: process.env.LOG_LEVEL || 'info',
  file: process.env.LOG_FILE || './logs/app.log'
};

// Debug: Mostrar configuración en desarrollo
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 Configuración del Sistema:');
  console.log('  JWT_SECRET:', JWT_SECRET ? 'Configurado ✅' : 'No configurado ❌');
  console.log('  JWT_EXPIRES_IN:', JWT_EXPIRES_IN);
  console.log('  Base de datos:', `${DB_CONFIG.host}:${DB_CONFIG.port}/${DB_CONFIG.database}`);
  console.log('  n8n URL:', N8N_CONFIG.url);
  console.log('  Directorio de uploads:', UPLOAD_CONFIG.uploadDir);
}

module.exports = {
  JWT_SECRET,
  JWT_EXPIRES_IN,
  DB_CONFIG,
  N8N_CONFIG,
  WHATSAPP_CONFIG,
  ROLES,
  PERMISSIONS,
  hasPermission,
  isSuperAdmin,
  UPLOAD_CONFIG,
  LOG_CONFIG
};
