import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { Save, RefreshCw, CheckCircle, AlertTriangle, Upload, Building } from 'lucide-react';

const InstitucionConfigForm = () => {
  const [formData, setFormData] = useState({
    nombre: '',
    logo_url: ''
  });
  const [message, setMessage] = useState('');
  const [status, setStatus] = useState('idle'); // idle, loading, success, error
  const { token } = useAuth();

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setStatus('loading');
        const response = await fetch('/api/config', {
          headers: {
            Authorization: "Bearer " + token,
          },
        });
        if (!response.ok) {
          throw new Error("Error fetching config: " + response.status);
        }
        const data = await response.json();
        setFormData({
          nombre: data.institucion?.nombre || "",
          logo_url: data.institucion?.logo_url || ""
        });
        setStatus('idle');
      } catch (error) {
        setMessage("Error al cargar la configuración: " + error.message);
        setStatus('error');
      }
    };

    fetchConfig();
  }, [token]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setStatus('loading');
      const response = await fetch('/api/config/institucion', {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
        body: JSON.stringify({
          nombre: formData.nombre,
          logo_url: formData.logo_url
        }),
      });
      if (!response.ok) {
        throw new Error("Error al guardar la configuración: " + response.status);
      }
      setMessage("Configuración de la institución guardada con éxito!");
      setStatus('success');
      setTimeout(() => setStatus('idle'), 3000);
    } catch (error) {
      setMessage("Error al guardar la configuración: " + error.message);
      setStatus('error');
    }
  };

  const handleLogoUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setMessage("Por favor seleccione un archivo de imagen válido.");
      setStatus('error');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setMessage("El archivo es demasiado grande. El tamaño máximo es 5MB.");
      setStatus('error');
      return;
    }

    const formDataUpload = new FormData();
    formDataUpload.append('file', file);

    try {
      setStatus('loading');
      const response = await fetch('/api/upload/logo', {
        method: 'POST',
        headers: {
          Authorization: "Bearer " + token,
        },
        body: formDataUpload,
      });

      if (!response.ok) {
        throw new Error("Error al subir el logo: " + response.status);
      }

      const data = await response.json();
      setFormData(prev => ({
        ...prev,
        logo_url: data.url
      }));
      setMessage("Logo subido con éxito!");
      setStatus('success');
      setTimeout(() => setStatus('idle'), 3000);
    } catch (error) {
      setMessage("Error al subir el logo: " + error.message);
      setStatus('error');
    }
  };

  return (
    <div className="p-6 bg-white dark:bg-slate-800 rounded-lg shadow-md">
      <div className="flex items-center mb-6">
        <Building className="h-6 w-6 text-indigo-600 mr-2" />
        <h2 className="text-2xl font-bold text-slate-800 dark:text-white">Configuración de la Institución</h2>
      </div>
      
      {message && (
        <div className={`p-4 mb-6 rounded-md ${status === 'error' ? 'bg-red-50 text-red-700 border border-red-200' : 'bg-green-50 text-green-700 border border-green-200'}`}>
          {status === 'error' ? <AlertTriangle className="inline-block mr-2 h-5 w-5" /> : <CheckCircle className="inline-block mr-2 h-5 w-5" />}
          {message}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-md">
          <h3 className="text-lg font-semibold text-slate-700 dark:text-white mb-4">Información Básica</h3>
          
          <div className="mb-4">
            <label htmlFor="nombre" className="block text-sm font-medium text-slate-700 dark:text-gray-300 mb-1">
              Nombre de la Institución:
            </label>
            <input
              type="text"
              id="nombre"
              name="nombre"
              value={formData.nombre}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Ingrese el nombre de la institución"
              required
            />
            <p className="mt-1 text-xs text-gray-500">Este nombre aparecerá en todos los documentos y comunicaciones.</p>
          </div>
        </div>

        <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-md">
          <h3 className="text-lg font-semibold text-slate-700 dark:text-white mb-4">Logo de la Institución</h3>
          
          <div className="mb-4">
            <label htmlFor="logo_url" className="block text-sm font-medium text-slate-700 dark:text-gray-300 mb-1">
              URL del Logo:
            </label>
            <input
              type="url"
              id="logo_url"
              name="logo_url"
              value={formData.logo_url}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="https://ejemplo.com/logo.png"
            />
            <p className="mt-1 text-xs text-gray-500">URL directa al logo de la institución.</p>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-700 dark:text-gray-300 mb-2">
              O subir un nuevo logo:
            </label>
            <div className="flex items-center space-x-4">
              <input
                type="file"
                accept="image/*"
                onChange={handleLogoUpload}
                className="hidden"
                id="logo-upload"
              />
              <label
                htmlFor="logo-upload"
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 flex items-center cursor-pointer"
              >
                <Upload className="h-5 w-5 mr-2" />
                Subir Logo
              </label>
              <span className="text-sm text-gray-500">Máximo 5MB, formatos: JPG, PNG, GIF</span>
            </div>
          </div>

          {formData.logo_url && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-slate-700 dark:text-gray-300 mb-2">
                Vista previa:
              </label>
              <div className="border border-gray-300 rounded-md p-4 bg-white">
                <img
                  src={formData.logo_url}
                  alt="Logo de la institución"
                  className="max-h-32 max-w-full object-contain"
                  onError={(e) => {
                    e.target.style.display = 'none';
                  }}
                />
              </div>
            </div>
          )}
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 flex items-center"
            disabled={status === 'loading'}
          >
            {status === 'loading' ? <RefreshCw className="animate-spin h-5 w-5 mr-2" /> : <Save className="h-5 w-5 mr-2" />}
            Guardar Configuración
          </button>
        </div>
      </form>
    </div>
  );
};

export default InstitucionConfigForm;
