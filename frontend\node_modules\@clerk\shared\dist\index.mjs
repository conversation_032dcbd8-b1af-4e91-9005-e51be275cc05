import {
  allSettled,
  fastDeepMergeAndKeep,
  fastDeepMergeAndReplace,
  logErrorInDevMode
} from "./chunk-ARQUL5DC.mjs";
import {
  createDeferredPromise
} from "./chunk-7QJ2QTJL.mjs";
import {
  createPathMatcher
} from "./chunk-2ZNADCNC.mjs";
import "./chunk-JJHTUJGL.mjs";
import {
  Poller
} from "./chunk-JY46X3OC.mjs";
import {
  createWorkerTimers
} from "./chunk-ZHPWRK4R.mjs";
import {
  noop
} from "./chunk-7FNX7RWY.mjs";
import {
  camelToSnake,
  deepCamelToSnake,
  deepSnakeToCamel,
  getNonUndefinedValues,
  isIPV4Address,
  isTruthy,
  snakeToCamel,
  titleize,
  toSentence
} from "./chunk-GGFRMWFO.mjs";
import {
  buildClerkJsScriptAttributes,
  clerkJsScriptUrl,
  loadClerkJsScript,
  setClerkJsLoadingErrorPackageName
} from "./chunk-XY5YYTBS.mjs";
import {
  versionSelector
} from "./chunk-Q5RDXBYA.mjs";
import {
  isHttpOrHttps,
  isProxyUrlRelative,
  isValidProxyUrl,
  proxyUrlToAbsoluteURL
} from "./chunk-6NDGN2IU.mjs";
import {
  addClerkPrefix,
  cleanDoubleSlashes,
  getClerkJsMajorVersionOrTag,
  getScriptUrl,
  hasLeadingSlash,
  hasTrailingSlash,
  isAbsoluteUrl,
  isCurrentDevAccountPortalOrigin,
  isLegacyDevAccountPortalOrigin,
  isNonEmptyURL,
  joinURL,
  parseSearchParams,
  stripScheme,
  withLeadingSlash,
  withTrailingSlash,
  withoutLeadingSlash,
  withoutTrailingSlash
} from "./chunk-IFTVZ2LQ.mjs";
import {
  isStaging
} from "./chunk-3TMSNP4L.mjs";
import {
  loadScript
} from "./chunk-E3R3SJ7O.mjs";
import "./chunk-N2V3PKFE.mjs";
import {
  LocalStorageBroadcastChannel
} from "./chunk-KZL5MSSZ.mjs";
import {
  logger
} from "./chunk-CYDR2ZSA.mjs";
import {
  CLERK_NETLIFY_CACHE_BUST_PARAM,
  handleNetlifyCacheInDevInstance
} from "./chunk-6MSQMJ3X.mjs";
import {
  applyFunctionToObj,
  filterProps,
  removeUndefined,
  without
} from "./chunk-CFXQSUF6.mjs";
import {
  extension,
  readJSONFile
} from "./chunk-5JU2E5TY.mjs";
import {
  getEnvVariable
} from "./chunk-TALGHI24.mjs";
import {
  handleValueOrFn
} from "./chunk-O32JQBM6.mjs";
import {
  addYears,
  dateTo12HourTime,
  differenceInCalendarDays,
  formatRelative,
  normalizeDate
} from "./chunk-XQNAC75V.mjs";
import {
  deprecated,
  deprecatedObjectProperty,
  deprecatedProperty
} from "./chunk-UEY4AZIP.mjs";
import {
  isDevelopmentEnvironment,
  isProductionEnvironment,
  isTestEnvironment
} from "./chunk-7HPDNZ3R.mjs";
import {
  deriveState
} from "./chunk-ZIXJBK4O.mjs";
import {
  DEV_BROWSER_JWT_KEY,
  extractDevBrowserJWTFromURL,
  setDevBrowserJWTInURL
} from "./chunk-K64INQ4C.mjs";
import {
  ClerkAPIResponseError,
  ClerkRuntimeError,
  ClerkWebAuthnError,
  EmailLinkError,
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus,
  buildErrorThrower,
  errorToJSON,
  is4xxError,
  isCaptchaError,
  isClerkAPIResponseError,
  isClerkRuntimeError,
  isEmailLinkError,
  isKnownError,
  isMetamaskError,
  isNetworkError,
  isPasswordPwnedError,
  isReverificationCancelledError,
  isUnauthorizedError,
  isUserLockedError,
  parseError,
  parseErrors
} from "./chunk-NT4JRXL3.mjs";
import {
  apiUrlFromPublishableKey
} from "./chunk-IBYQ6PKA.mjs";
import {
  buildPublishableKey,
  createDevOrStagingUrlCache,
  getCookieSuffix,
  getSuffixedCookieName,
  isDevelopmentFromPublishableKey,
  isDevelopmentFromSecretKey,
  isProductionFromPublishableKey,
  isProductionFromSecretKey,
  isPublishableKey,
  parsePublishableKey
} from "./chunk-QU372XZW.mjs";
import {
  isomorphicAtob
} from "./chunk-TETGTEI2.mjs";
import {
  isomorphicBtoa
} from "./chunk-KOH7GTJO.mjs";
import {
  CURRENT_DEV_INSTANCE_SUFFIXES,
  DEV_OR_STAGING_SUFFIXES,
  LEGACY_DEV_INSTANCE_SUFFIXES,
  LOCAL_API_URL,
  LOCAL_ENV_SUFFIXES,
  PROD_API_URL,
  STAGING_API_URL,
  STAGING_ENV_SUFFIXES,
  iconImageUrl
} from "./chunk-I6MTSTOF.mjs";
import {
  inBrowser,
  isBrowserOnline,
  isValidBrowser,
  isValidBrowserOnline,
  userAgentIsRobot
} from "./chunk-JKSAJ6AV.mjs";
import {
  colorToSameTypeString,
  hasAlpha,
  hexStringToRgbaColor,
  isHSLColor,
  isRGBColor,
  isTransparent,
  isValidHexString,
  isValidHslaString,
  isValidRgbaString,
  stringToHslaColor,
  stringToSameTypeColor
} from "./chunk-X6NLIF7Y.mjs";
import "./chunk-7ELT755Q.mjs";
export {
  CLERK_NETLIFY_CACHE_BUST_PARAM,
  CURRENT_DEV_INSTANCE_SUFFIXES,
  ClerkAPIResponseError,
  ClerkRuntimeError,
  ClerkWebAuthnError,
  DEV_BROWSER_JWT_KEY,
  DEV_OR_STAGING_SUFFIXES,
  EmailLinkError,
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus,
  LEGACY_DEV_INSTANCE_SUFFIXES,
  LOCAL_API_URL,
  LOCAL_ENV_SUFFIXES,
  LocalStorageBroadcastChannel,
  PROD_API_URL,
  Poller,
  STAGING_API_URL,
  STAGING_ENV_SUFFIXES,
  addClerkPrefix,
  addYears,
  allSettled,
  apiUrlFromPublishableKey,
  applyFunctionToObj,
  buildClerkJsScriptAttributes,
  buildErrorThrower,
  buildPublishableKey,
  camelToSnake,
  cleanDoubleSlashes,
  clerkJsScriptUrl,
  colorToSameTypeString,
  createDeferredPromise,
  createDevOrStagingUrlCache,
  createPathMatcher,
  createWorkerTimers,
  dateTo12HourTime,
  deepCamelToSnake,
  deepSnakeToCamel,
  deprecated,
  deprecatedObjectProperty,
  deprecatedProperty,
  deriveState,
  differenceInCalendarDays,
  errorToJSON,
  extension,
  extractDevBrowserJWTFromURL,
  fastDeepMergeAndKeep,
  fastDeepMergeAndReplace,
  filterProps,
  formatRelative,
  getClerkJsMajorVersionOrTag,
  getCookieSuffix,
  getEnvVariable,
  getNonUndefinedValues,
  getScriptUrl,
  getSuffixedCookieName,
  handleNetlifyCacheInDevInstance,
  handleValueOrFn,
  hasAlpha,
  hasLeadingSlash,
  hasTrailingSlash,
  hexStringToRgbaColor,
  iconImageUrl,
  inBrowser,
  is4xxError,
  isAbsoluteUrl,
  isBrowserOnline,
  isCaptchaError,
  isClerkAPIResponseError,
  isClerkRuntimeError,
  isCurrentDevAccountPortalOrigin,
  isDevelopmentEnvironment,
  isDevelopmentFromPublishableKey,
  isDevelopmentFromSecretKey,
  isEmailLinkError,
  isHSLColor,
  isHttpOrHttps,
  isIPV4Address,
  isKnownError,
  isLegacyDevAccountPortalOrigin,
  isMetamaskError,
  isNetworkError,
  isNonEmptyURL,
  isPasswordPwnedError,
  isProductionEnvironment,
  isProductionFromPublishableKey,
  isProductionFromSecretKey,
  isProxyUrlRelative,
  isPublishableKey,
  isRGBColor,
  isReverificationCancelledError,
  isStaging,
  isTestEnvironment,
  isTransparent,
  isTruthy,
  isUnauthorizedError,
  isUserLockedError,
  isValidBrowser,
  isValidBrowserOnline,
  isValidHexString,
  isValidHslaString,
  isValidProxyUrl,
  isValidRgbaString,
  isomorphicAtob,
  isomorphicBtoa,
  joinURL,
  loadClerkJsScript,
  loadScript,
  logErrorInDevMode,
  logger,
  noop,
  normalizeDate,
  parseError,
  parseErrors,
  parsePublishableKey,
  parseSearchParams,
  proxyUrlToAbsoluteURL,
  readJSONFile,
  removeUndefined,
  setClerkJsLoadingErrorPackageName,
  setDevBrowserJWTInURL,
  snakeToCamel,
  stringToHslaColor,
  stringToSameTypeColor,
  stripScheme,
  titleize,
  toSentence,
  userAgentIsRobot,
  versionSelector,
  withLeadingSlash,
  withTrailingSlash,
  without,
  withoutLeadingSlash,
  withoutTrailingSlash
};
//# sourceMappingURL=index.mjs.map