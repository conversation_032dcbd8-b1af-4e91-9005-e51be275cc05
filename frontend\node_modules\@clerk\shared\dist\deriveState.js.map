{"version": 3, "sources": ["../src/deriveState.ts"], "sourcesContent": ["import type {\n  InitialState,\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  OrganizationResource,\n  Resources,\n  SignedInSessionResource,\n  UserResource,\n} from '@clerk/types';\n\n/**\n * Derives authentication state based on the current rendering context (SSR or client-side).\n */\nexport const deriveState = (clerkOperational: boolean, state: Resources, initialState: InitialState | undefined) => {\n  if (!clerkOperational && initialState) {\n    return deriveFromSsrInitialState(initialState);\n  }\n  return deriveFromClientSideState(state);\n};\n\nconst deriveFromSsrInitialState = (initialState: InitialState) => {\n  const userId = initialState.userId;\n  const user = initialState.user as UserResource;\n  const sessionId = initialState.sessionId;\n  const sessionStatus = initialState.sessionStatus;\n  const sessionClaims = initialState.sessionClaims;\n  const session = initialState.session as SignedInSessionResource;\n  const organization = initialState.organization as OrganizationResource;\n  const orgId = initialState.orgId;\n  const orgRole = initialState.orgRole as OrganizationCustomRoleKey;\n  const orgPermissions = initialState.orgPermissions as OrganizationCustomPermissionKey[];\n  const orgSlug = initialState.orgSlug;\n  const actor = initialState.actor;\n  const factorVerificationAge = initialState.factorVerificationAge;\n\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    sessionStatus,\n    sessionClaims,\n    organization,\n    orgId,\n    orgRole,\n    orgPermissions,\n    orgSlug,\n    actor,\n    factorVerificationAge,\n  };\n};\n\nconst deriveFromClientSideState = (state: Resources) => {\n  const userId: string | null | undefined = state.user ? state.user.id : state.user;\n  const user = state.user;\n  const sessionId: string | null | undefined = state.session ? state.session.id : state.session;\n  const session = state.session;\n  const sessionStatus = state.session?.status;\n  const sessionClaims: JwtPayload | null | undefined = state.session\n    ? state.session.lastActiveToken?.jwt?.claims\n    : null;\n  const factorVerificationAge: [number, number] | null = state.session ? state.session.factorVerificationAge : null;\n  const actor = session?.actor;\n  const organization = state.organization;\n  const orgId: string | null | undefined = state.organization ? state.organization.id : state.organization;\n  const orgSlug = organization?.slug;\n  const membership = organization\n    ? user?.organizationMemberships?.find(om => om.organization.id === orgId)\n    : organization;\n  const orgPermissions = membership ? membership.permissions : membership;\n  const orgRole = membership ? membership.role : membership;\n\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    sessionStatus,\n    sessionClaims,\n    organization,\n    orgId,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    actor,\n    factorVerificationAge,\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,cAAc,CAAC,kBAA2B,OAAkB,iBAA2C;AAClH,MAAI,CAAC,oBAAoB,cAAc;AACrC,WAAO,0BAA0B,YAAY;AAAA,EAC/C;AACA,SAAO,0BAA0B,KAAK;AACxC;AAEA,IAAM,4BAA4B,CAAC,iBAA+B;AAChE,QAAM,SAAS,aAAa;AAC5B,QAAM,OAAO,aAAa;AAC1B,QAAM,YAAY,aAAa;AAC/B,QAAM,gBAAgB,aAAa;AACnC,QAAM,gBAAgB,aAAa;AACnC,QAAM,UAAU,aAAa;AAC7B,QAAM,eAAe,aAAa;AAClC,QAAM,QAAQ,aAAa;AAC3B,QAAM,UAAU,aAAa;AAC7B,QAAM,iBAAiB,aAAa;AACpC,QAAM,UAAU,aAAa;AAC7B,QAAM,QAAQ,aAAa;AAC3B,QAAM,wBAAwB,aAAa;AAE3C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,4BAA4B,CAAC,UAAqB;AACtD,QAAM,SAAoC,MAAM,OAAO,MAAM,KAAK,KAAK,MAAM;AAC7E,QAAM,OAAO,MAAM;AACnB,QAAM,YAAuC,MAAM,UAAU,MAAM,QAAQ,KAAK,MAAM;AACtF,QAAM,UAAU,MAAM;AACtB,QAAM,gBAAgB,MAAM,SAAS;AACrC,QAAM,gBAA+C,MAAM,UACvD,MAAM,QAAQ,iBAAiB,KAAK,SACpC;AACJ,QAAM,wBAAiD,MAAM,UAAU,MAAM,QAAQ,wBAAwB;AAC7G,QAAM,QAAQ,SAAS;AACvB,QAAM,eAAe,MAAM;AAC3B,QAAM,QAAmC,MAAM,eAAe,MAAM,aAAa,KAAK,MAAM;AAC5F,QAAM,UAAU,cAAc;AAC9B,QAAM,aAAa,eACf,MAAM,yBAAyB,KAAK,QAAM,GAAG,aAAa,OAAO,KAAK,IACtE;AACJ,QAAM,iBAAiB,aAAa,WAAW,cAAc;AAC7D,QAAM,UAAU,aAAa,WAAW,OAAO;AAE/C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": []}