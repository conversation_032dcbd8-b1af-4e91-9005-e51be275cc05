<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limpiar Storage - CCJAP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            background: #c82333;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Limpiar Storage del Sistema</h1>
        <p>Esta herramienta limpia el localStorage y sessionStorage para solucionar problemas de token.</p>
        
        <div id="status" class="status"></div>
        
        <div>
            <button class="btn" onclick="clearStorage()">
                🗑️ Limpiar Todo el Storage
            </button>
            
            <button class="btn btn-success" onclick="goToLogin()">
                🔑 Ir al Login
            </button>
        </div>
        
        <div style="margin-top: 30px; text-align: left;">
            <h3>¿Qué hace esta herramienta?</h3>
            <ul>
                <li>Elimina todos los tokens JWT almacenados</li>
                <li>Limpia datos de usuario en caché</li>
                <li>Resetea configuraciones locales</li>
                <li>Soluciona errores de "Token no válido"</li>
            </ul>
            
            <h3>¿Cuándo usarla?</h3>
            <ul>
                <li>Cuando aparecen errores 401 (No autorizado)</li>
                <li>Cuando el token no es válido</li>
                <li>Después de cambios en el backend</li>
                <li>Para forzar un nuevo login</li>
            </ul>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.innerHTML = message;
            status.style.display = 'block';
        }

        function clearStorage() {
            try {
                // Limpiar localStorage
                const localStorageKeys = Object.keys(localStorage);
                localStorageKeys.forEach(key => {
                    localStorage.removeItem(key);
                });
                
                // Limpiar sessionStorage
                const sessionStorageKeys = Object.keys(sessionStorage);
                sessionStorageKeys.forEach(key => {
                    sessionStorage.removeItem(key);
                });
                
                // Limpiar cookies relacionadas con la aplicación
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });
                
                showStatus(`
                    ✅ <strong>Storage limpiado exitosamente!</strong><br>
                    📊 localStorage: ${localStorageKeys.length} elementos eliminados<br>
                    📊 sessionStorage: ${sessionStorageKeys.length} elementos eliminados<br>
                    🍪 Cookies limpiadas<br><br>
                    <strong>Ahora puedes hacer login nuevamente.</strong>
                `, 'success');
                
                console.log('🧹 Storage limpiado completamente');
                
            } catch (error) {
                showStatus(`❌ Error al limpiar storage: ${error.message}`, 'error');
                console.error('Error al limpiar storage:', error);
            }
        }

        function goToLogin() {
            // Redirigir al login principal
            window.location.href = '/';
        }

        // Mostrar información inicial
        window.onload = function() {
            const localCount = Object.keys(localStorage).length;
            const sessionCount = Object.keys(sessionStorage).length;
            
            if (localCount > 0 || sessionCount > 0) {
                showStatus(`
                    📊 Elementos encontrados:<br>
                    localStorage: ${localCount} elementos<br>
                    sessionStorage: ${sessionCount} elementos<br><br>
                    <strong>Recomendado limpiar si hay problemas de token.</strong>
                `, 'info');
            } else {
                showStatus('✅ El storage ya está limpio.', 'success');
            }
        };
    </script>
</body>
</html>
